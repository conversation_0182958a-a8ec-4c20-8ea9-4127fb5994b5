# Tính năng Download Files từ Google Drive

## M<PERSON> tả tính năng
Tính năng tải file về theo nguyên tắc sau:

- Truy cập bảng scanned_users trên supabase
- Tạo mỗi user 1 thư mục theo nguyên tắc: <username là tên thư mục>
- Bắt đầu tải file về cho từng user:
  - Query user trong bảng scanned_files để lấy về danh sách file thuộc user đó, chỉ lấy các file chưa migrate (dựa vào cột status)
  - Lấy file id hoặc folder id trong cột file_id, tải file về.
  - Nếu là folder thì tạo mới folder ở local
  - Nếu là file thì tải file về
  - Đảm bảo sau khi tải về cấu trúc file / thư mục vẫn giữ nguyên như trên google drive.
- Tạo một màn hình để thiết lập cấu hình tải file:
  - <PERSON><PERSON><PERSON> danh sách người dùng cần tải (<PERSON><PERSON> thể chọn tất cả)
  - <PERSON><PERSON><PERSON> thư mục đích tải về
  - <PERSON><PERSON><PERSON> số tiến trình tải đồng thời
  - Chọn số lần retry khi tải file lỗi
  - Hiển thị các thống kê realtime:
  - Người dùng đang tải file
  - Tổng số file / folder cần tải
  - Tiến độ hiện tại
  - Thời gian đã chạy
  - File nào bị lỗi, file nào không.
  - Nếu file bị lỗi, cập nhật nội dung lỗi vào bản ghi supabase, cột error message.
  - Tạo component báo cáo sau khi tải file.

## Plan Implementation Chi Tiết

### Phase 1: Database Schema Updates
1. **Tạo bảng download_sessions**
   ```sql
   CREATE TABLE download_sessions (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     name TEXT NOT NULL,
     selected_users TEXT[] NOT NULL,
     download_path TEXT NOT NULL,
     concurrent_downloads INTEGER DEFAULT 3,
     max_retries INTEGER DEFAULT 3,
     status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled')),
     total_files INTEGER DEFAULT 0,
     downloaded_files INTEGER DEFAULT 0,
     failed_files INTEGER DEFAULT 0,
     total_size BIGINT DEFAULT 0,
     downloaded_size BIGINT DEFAULT 0,
     started_at TIMESTAMPTZ,
     completed_at TIMESTAMPTZ,
     created_at TIMESTAMPTZ DEFAULT NOW(),
     updated_at TIMESTAMPTZ DEFAULT NOW()
   );
   ```

2. **Tạo bảng download_items**
   ```sql
   CREATE TABLE download_items (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     download_session_id UUID NOT NULL REFERENCES download_sessions(id) ON DELETE CASCADE,
     scanned_file_id UUID NOT NULL REFERENCES scanned_files(id),
     user_email TEXT NOT NULL,
     file_id TEXT NOT NULL,
     file_name TEXT NOT NULL,
     file_path TEXT NOT NULL,
     local_path TEXT,
     file_size BIGINT DEFAULT 0,
     status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'completed', 'failed', 'skipped')),
     retry_count INTEGER DEFAULT 0,
     error_message TEXT,
     download_started_at TIMESTAMPTZ,
     download_completed_at TIMESTAMPTZ,
     created_at TIMESTAMPTZ DEFAULT NOW()
   );
   ```

3. **Update scanned_files table**
   - Thêm cột `download_status` và `local_path`

### Phase 2: Backend Services
1. **FileDownloadService** (`src/services/file-download-service.js`)
   - Quản lý download sessions
   - Coordinate download workers
   - Track progress và statistics
   - Handle errors và retries

2. **DownloadWorker** (`src/services/download-worker.js`)
   - Xử lý download từng file
   - Maintain folder structure
   - Progress reporting
   - Error handling

3. **API Endpoints** (`src/api/download-api.js`)
   - `POST /api/download/sessions` - Tạo download session
   - `GET /api/download/sessions` - List sessions
   - `GET /api/download/sessions/:id` - Get session details
   - `POST /api/download/sessions/:id/start` - Start download
   - `POST /api/download/sessions/:id/pause` - Pause download
   - `POST /api/download/sessions/:id/cancel` - Cancel download
   - `GET /api/download/sessions/:id/progress` - Get realtime progress

### Phase 3: Frontend Components
1. **DownloadDashboard** (`frontend/src/pages/Download.jsx`)
   - Main download page
   - Session management
   - Configuration panel

2. **DownloadConfigForm** (`frontend/src/components/DownloadConfigForm.jsx`)
   - User selection
   - Download path selection
   - Concurrent downloads setting
   - Retry configuration

3. **DownloadProgress** (`frontend/src/components/DownloadProgress.jsx`)
   - Realtime progress display
   - File-by-file status
   - Statistics dashboard
   - Control buttons (pause/resume/cancel)

4. **DownloadReport** (`frontend/src/components/DownloadReport.jsx`)
   - Success/failure summary
   - Error details
   - Export functionality

### Phase 4: Integration & Testing
1. **Navigation Update**
   - Thêm Download menu item
   - Route configuration

2. **Realtime Updates**
   - Supabase realtime subscriptions
   - Progress broadcasting

3. **Error Handling**
   - Comprehensive error logging
   - User-friendly error messages
   - Recovery mechanisms

## Technical Specifications

### File Structure Preservation
- Tạo folder structure tương ứng với Google Drive
- Maintain relative paths
- Handle duplicate names
- Preserve metadata (timestamps, permissions info)

### Concurrent Download Management
- Worker pool pattern
- Rate limiting để tránh API limits
- Queue management
- Resource cleanup

### Progress Tracking
- File-level progress
- Session-level statistics
- Real-time updates via WebSocket/SSE
- Persistent progress storage

### Error Handling & Recovery
- Automatic retry với exponential backoff
- Detailed error logging
- Partial download resume
- Graceful degradation

## Implementation Order
1. ✅ Database schema updates
2. ✅ Backend services (FileDownloadService, DownloadWorker)
3. ✅ API endpoints
4. ✅ Frontend components
5. ✅ Integration & testing
6. ✅ Documentation & user guide