# Database Schema Documentation

## Tổng quan

Database schema cho hệ thống Drive-to-Lark Migration được thiết kế để:
- <PERSON> dõi tiến trình migration
- <PERSON><PERSON><PERSON><PERSON> lý mapping người dùng Google ↔ Lark
- Lưu trữ metadata và logs chi tiết
- Hỗ trợ realtime updates
- <PERSON><PERSON><PERSON> bảo bảo mật với Row Level Security (RLS)

## Kiến trúc Database

### Công nghệ
- **Database**: PostgreSQL (Supabase)
- **ORM**: Supabase Client JS
- **Realtime**: Supabase Realtime
- **Security**: Row Level Security (RLS)

## Bảng chính

### 1. `users` - Mapping người dùng
Quản lý mapping giữa Google email và Lark user ID.

```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email_google TEXT UNIQUE NOT NULL,
  lark_userid TEXT,
  mapped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**<PERSON><PERSON>c đích**: 
- Map email Google với Lark user ID
- <PERSON> dõi trạng thái mapping
- Hỗ trợ UI mapping thủ công

### 2. `migration_tasks` - Tác vụ migration
Quản lý các tác vụ migration cấp cao.

```sql
CREATE TABLE migration_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_email TEXT NOT NULL,
  scope TEXT NOT NULL CHECK (scope IN ('all', 'path')),
  path TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  progress NUMERIC(5,2) DEFAULT 0.00,
  total_files INTEGER DEFAULT 0,
  completed_files INTEGER DEFAULT 0,
  failed_files INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0,
  transferred_size BIGINT DEFAULT 0,
  started_at TIMESTAMPTZ,
  finished_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT,
  config JSONB
);
```

**Trạng thái**: `pending`, `running`, `completed`, `failed`, `cancelled`

**Mục đích**:
- Theo dõi tiến trình tổng thể
- Lưu cấu hình migration
- Thống kê performance

### 3. `migration_items` - Chi tiết từng file
Theo dõi migration từng file cụ thể.

```sql
CREATE TABLE migration_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES migration_tasks(id),
  src_file_id TEXT NOT NULL,
  src_file_name TEXT NOT NULL,
  src_file_path TEXT NOT NULL,
  src_parent_id TEXT,
  dest_file_id TEXT,
  dest_folder_token TEXT,
  file_type TEXT NOT NULL,
  size BIGINT NOT NULL DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'pending',
  error_code TEXT,
  error_message TEXT,
  retries INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB
);
```

**Trạng thái**: `pending`, `downloading`, `uploading`, `completed`, `failed`, `skipped`

**Mục đích**:
- Theo dõi từng file
- Retry logic
- Lưu metadata Google Drive

### 4. `permission_mappings` - Mapping quyền
Quản lý mapping quyền truy cập từ Google Drive sang Lark.

```sql
CREATE TABLE permission_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  migration_item_id UUID NOT NULL REFERENCES migration_items(id),
  google_email TEXT NOT NULL,
  google_role TEXT NOT NULL,
  lark_user_id TEXT,
  lark_role TEXT,
  mapped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Google Roles**: `owner`, `writer`, `reader`, `commenter`
**Lark Roles**: `full_access`, `edit`, `view`, `comment`

### 5. `migration_logs` - System logs
Lưu trữ logs chi tiết của hệ thống.

```sql
CREATE TABLE migration_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID REFERENCES migration_tasks(id),
  item_id UUID REFERENCES migration_items(id),
  level TEXT NOT NULL CHECK (level IN ('info', 'warning', 'error', 'debug')),
  message TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Views

### `migration_task_stats` - Thống kê task
View tổng hợp thống kê cho migration tasks.

```sql
CREATE VIEW migration_task_stats AS
SELECT 
    t.id,
    t.owner_email,
    t.status,
    t.progress,
    t.total_files,
    t.completed_files,
    t.failed_files,
    CASE 
        WHEN t.total_files > 0 THEN ROUND((t.completed_files::NUMERIC / t.total_files::NUMERIC) * 100, 2)
        ELSE 0 
    END as completion_percentage,
    t.total_size,
    t.transferred_size,
    CASE 
        WHEN t.total_size > 0 THEN ROUND((t.transferred_size::NUMERIC / t.total_size::NUMERIC) * 100, 2)
        ELSE 0 
    END as transfer_percentage,
    t.started_at,
    t.finished_at,
    CASE 
        WHEN t.started_at IS NOT NULL AND t.finished_at IS NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - t.started_at))
        WHEN t.started_at IS NOT NULL AND t.finished_at IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (t.finished_at - t.started_at))
        ELSE NULL 
    END as duration_seconds
FROM migration_tasks t;
```

## Indexes

Các indexes được tạo để tối ưu performance:

```sql
-- Migration tasks
CREATE INDEX idx_migration_tasks_owner_email ON migration_tasks(owner_email);
CREATE INDEX idx_migration_tasks_status ON migration_tasks(status);
CREATE INDEX idx_migration_tasks_created_at ON migration_tasks(created_at);

-- Migration items
CREATE INDEX idx_migration_items_task_id ON migration_items(task_id);
CREATE INDEX idx_migration_items_status ON migration_items(status);
CREATE INDEX idx_migration_items_src_file_id ON migration_items(src_file_id);

-- Permission mappings
CREATE INDEX idx_permission_mappings_migration_item_id ON permission_mappings(migration_item_id);
CREATE INDEX idx_permission_mappings_google_email ON permission_mappings(google_email);
CREATE INDEX idx_permission_mappings_mapped ON permission_mappings(mapped);

-- Logs
CREATE INDEX idx_migration_logs_task_id ON migration_logs(task_id);
CREATE INDEX idx_migration_logs_level ON migration_logs(level);
CREATE INDEX idx_migration_logs_created_at ON migration_logs(created_at);

-- Users
CREATE INDEX idx_users_email_google ON users(email_google);
CREATE INDEX idx_users_mapped ON users(mapped);
```

## Row Level Security (RLS)

### Policies cho Users
- Users chỉ có thể xem migration tasks của chính họ
- Service role có full access

```sql
-- Users can only see their own migration tasks
CREATE POLICY "Users can view own migration tasks" ON migration_tasks
    FOR SELECT USING (owner_email = auth.email());

-- Service role full access
CREATE POLICY "Service role full access migration_tasks" ON migration_tasks
    FOR ALL USING (auth.role() = 'service_role');
```

## Realtime Subscriptions

Các bảng được enable cho Supabase Realtime:
- `migration_tasks`
- `migration_items` 
- `migration_logs`

```sql
ALTER PUBLICATION supabase_realtime ADD TABLE migration_tasks;
ALTER PUBLICATION supabase_realtime ADD TABLE migration_items;
ALTER PUBLICATION supabase_realtime ADD TABLE migration_logs;
```

## Triggers

### Auto-update `updated_at`
Tự động cập nhật timestamp khi record được update.

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## API Usage Examples

### Tạo Migration Task
```javascript
const task = await migrationService.initializeMigrationTask(
    '<EMAIL>',
    'path',
    '/Important Documents',
    { max_retries: 5 }
);
```

### Theo dõi Progress
```javascript
const stats = await migrationService.getTaskStatistics(taskId);
console.log(`Progress: ${stats.data.statistics.progress}%`);
```

### Realtime Updates
```javascript
const subscription = supabaseClient.subscribeToTask(taskId, (payload) => {
    console.log('Task updated:', payload);
});
```

## Maintenance

### Cleanup Old Data
```javascript
// Xóa tasks completed > 30 ngày
await migrationService.cleanupCompletedTasks(30);
```

### Monitoring Queries
```sql
-- Tasks đang chạy
SELECT * FROM migration_task_stats WHERE status = 'running';

-- Files lỗi nhiều nhất
SELECT error_code, COUNT(*) as count 
FROM migration_items 
WHERE status = 'failed' 
GROUP BY error_code 
ORDER BY count DESC;

-- Performance theo ngày
SELECT DATE(created_at) as date, 
       COUNT(*) as tasks,
       AVG(duration_seconds) as avg_duration
FROM migration_task_stats 
WHERE status = 'completed'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```
