# Google Credentials Migration Guide

## Tổng quan thay đổi

Hệ thống đã được cập nhật để **chỉ sử dụng file `google-service-account.json`** cho Google authentication, loại bỏ hoàn toàn việc đọc Google credentials từ environment variables.

## Thay đổi chính

### ✅ <PERSON><PERSON><PERSON><PERSON><PERSON> (sử dụng .env)
```env
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
GOOGLE_PROJECT_ID=your-google-project-id
GOOGLE_CLIENT_EMAIL=<EMAIL>
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_ADMIN_EMAIL=<EMAIL>
```

### ✅ <PERSON><PERSON><PERSON> giờ (chỉ sử dụng JSON file)
```json
// google-service-account.json
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## Cách sử dụng mới

### 1. Chạy tests với email argument
```bash
# Trước đây
node tests/test-google-auth.js

# Bây giờ
node tests/test-google-auth.js <EMAIL>
```

### 2. Chạy infrastructure check
```bash
# Trước đây
node src/infrastructure-check.js

# Bây giờ
node src/infrastructure-check.js <EMAIL>
```

### 3. Chạy domain delegation check
```bash
# Trước đây
node src/check-domain-delegation.js

# Bây giờ
node src/check-domain-delegation.js <EMAIL>
```

### 4. Chạy troubleshooting
```bash
# Trước đây
node tests/troubleshoot-google-drive.js

# Bây giờ
node tests/troubleshoot-google-drive.js <EMAIL>
```

## Lợi ích của thay đổi

### 🔒 Bảo mật tốt hơn
- Không cần lưu private key trong environment variables
- Giảm nguy cơ leak credentials qua logs hoặc process environment
- File JSON có thể được bảo vệ bằng file permissions

### 🚀 Đơn giản hóa setup
- Chỉ cần 1 file JSON thay vì nhiều environment variables
- Dễ dàng copy/backup credentials
- Ít lỗi do format sai (như newline trong private key)

### 🔧 Dễ maintain
- Credentials tập trung trong 1 file
- Không cần sync giữa nhiều environment files
- Dễ dàng validate format JSON

## Migration checklist

### ✅ Files đã được cập nhật:
- [x] `src/auth/google-auth.js` - Load credentials từ JSON file
- [x] `src/check-domain-delegation.js` - Sử dụng command line argument
- [x] `src/infrastructure-check.js` - Loại bỏ Google env vars
- [x] `tests/test-google-auth.js` - Sử dụng command line argument
- [x] `tests/simple-drive-test-fixed.js` - Sử dụng command line argument
- [x] `tests/test-config.js` - Loại bỏ Google env vars
- [x] `tests/test-all-apis.js` - Loại bỏ Google env vars
- [x] `tests/test-simple.js` - Kiểm tra JSON file thay vì env vars
- [x] `tests/troubleshoot-google-drive.js` - Sử dụng command line argument
- [x] `tests/test-google-drive-api.js` - Sử dụng command line argument
- [x] `tests/simple-drive-test.js` - Sử dụng command line argument
- [x] `scripts/setup-uat.js` - Loại bỏ Google env vars
- [x] `scripts/deploy-production.js` - Loại bỏ Google env vars
- [x] `.env.example` - Cập nhật comments
- [x] `config/uat.env.example` - Cập nhật comments
- [x] `config/production.env.example` - Cập nhật comments
- [x] `README.md` - Cập nhật hướng dẫn

### ✅ Environment variables không còn cần thiết:
- `GOOGLE_SERVICE_ACCOUNT_EMAIL`
- `GOOGLE_PRIVATE_KEY`
- `GOOGLE_PROJECT_ID`
- `GOOGLE_CLIENT_EMAIL`
- `GOOGLE_CLIENT_ID`
- `GOOGLE_PRIVATE_KEY_ID`
- `GOOGLE_ADMIN_EMAIL` (optional, có thể dùng command line)
- `GOOGLE_TEST_EMAIL` (optional, có thể dùng command line)
- `GOOGLE_TEST_USERS` (optional, có thể dùng command line)

## Lưu ý quan trọng

1. **File `google-service-account.json` phải tồn tại** trong root directory của project
2. **Không commit file này vào git** - đã có trong `.gitignore`
3. **Backup file này an toàn** - đây là credential quan trọng
4. **Test emails giờ được truyền qua command line** thay vì environment variables
5. **Các script khác vẫn hoạt động bình thường** - chỉ cần truyền email qua argument

## Troubleshooting

### Lỗi "Service account key file not found"
```bash
# Kiểm tra file tồn tại
ls -la google-service-account.json

# Nếu không có, copy từ Google Cloud Console
```

### Lỗi "Missing required field"
```bash
# Kiểm tra format JSON
cat google-service-account.json | jq .

# Đảm bảo có đủ các field: client_email, private_key, project_id, client_id
```

### Lỗi "No test email provided"
```bash
# Truyền email qua command line argument
node tests/test-google-auth.js <EMAIL>
```
