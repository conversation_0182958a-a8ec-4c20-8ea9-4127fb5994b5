# Hướng dẫn sử dụng tính năng Upload File lên Lark

## Giới thiệu

Tài liệu này cung cấp hướng dẫn chi tiết về cách sử dụng tính năng upload file từ hệ thống local lên Lark Drive, cũng như cách áp dụng các quyền truy cập cho các file và thư mục đã được upload.

## Tổng quan về luồng hoạt động

Quy trình upload và phân quyền được chia thành hai giai đoạn chính:

1.  **Upload File**: Các file từ local sẽ được upload lên Lark Drive. Hệ thống sẽ tự động xử lý việc tạo các thư mục cần thiết để đảm bảo cấu trúc thư mục được giữ nguyên.
2.  **Phân quyền**: <PERSON><PERSON> <PERSON><PERSON> upload thành công, hệ thống sẽ áp dụng các quyền truy cập cho từng file và thư mục, bao gồm cả việc chia sẻ công khai và thêm các thành viên cụ thể với các quyền tương ứng.

## 3. Quy trình Upload File

### 3.1. Tổng quan

Quy trình upload file được quản lý bởi `lark-upload-engine.js`, một engine được thiết kế để tối ưu hóa việc tải file lên Lark Drive. Engine này có các tính năng nổi bật sau:

*   **Smart Upload**: Tự động phát hiện và xử lý các file nhỏ và lớn theo những cách khác nhau để tối ưu hiệu suất.
*   **Chunked Uploads**: Hỗ trợ upload các file lớn bằng cách chia nhỏ file thành các phần (chunks) và tải lên song song.
*   **Tái tạo cấu trúc thư mục**: Tự động tạo lại cấu trúc thư mục trên Lark Drive giống với cấu trúc thư mục ở local.

### 3.2. Luồng hoạt động chi tiết

1.  **Khởi tạo Upload**: Khi một yêu cầu upload được gửi đến, `LarkUploadEngine` sẽ tiếp nhận và bắt đầu xử lý.

2.  **Kiểm tra và tạo thư mục**:
    *   Hệ thống sẽ kiểm tra xem thư mục cha của file cần upload đã tồn tại trên Lark Drive hay chưa.
    *   Nếu chưa, `createFolderStructure` sẽ được gọi để tạo mới thư mục. Thư mục đã được tạo sẽ được lưu vào cache để tái sử dụng, tránh việc tạo lại nhiều lần.

3.  **Xử lý Upload File**:
    *   **File nhỏ (< 20MB)**: Các file có dung lượng nhỏ hơn 20MB sẽ được upload trực tiếp bằng API `upload_all` của Lark. Đây là cách upload nhanh và hiệu quả cho các file nhỏ.
    *   **File lớn (>= 20MB)**: Các file lớn sẽ được xử lý qua quy trình chunked upload:
        1.  **`upload_prepare`**: Gửi yêu cầu để chuẩn bị cho việc upload, nhận về một `upload_id`.
        2.  **`upload_part`**: File được chia thành các chunk nhỏ (mặc định là 10MB). Mỗi chunk sẽ được upload riêng lẻ.
        3.  **`upload_finish`**: Sau khi tất cả các chunk đã được upload thành công, một yêu cầu cuối cùng sẽ được gửi để hoàn tất quá trình upload.

4.  **Hoàn tất**: Sau khi upload thành công, thông tin về file (như `file_token`) sẽ được trả về và lưu lại để sử dụng trong các bước tiếp theo (ví dụ: phân quyền).

## 4. Quy trình áp dụng Permission

### 4.1. Tổng quan

Sau khi file được upload thành công, bước tiếp theo là áp dụng các quyền truy cập. Quá trình này được thực hiện thông qua `lark-drive-api.js`, nơi chứa các hàm để tương tác với API phân quyền của Lark.

### 4.2. Các loại phân quyền

Hệ thống hỗ trợ các loại phân quyền sau:

*   **`setPermissions`**: Thiết lập các quyền công khai cho file hoặc thư mục. Bạn có thể cấu hình các quyền như:
    *   `external_access`: Cho phép truy cập từ bên ngoài tổ chức.
    *   `link_share_entity`: Ai có thể truy cập qua link (ví dụ: `anyone_readable`, `anyone_editable`).
*   **`addPermissions`**: Thêm các thành viên cụ thể vào file hoặc thư mục với các quyền nhất định (ví dụ: `view`, `edit`).
*   **`transferOwnership`**: Chuyển quyền sở hữu của file hoặc thư mục cho một người dùng khác.

### 4.3. Luồng hoạt động chi tiết

1.  **Lấy `file_token`**: Sau khi upload thành công, `file_token` của file hoặc thư mục sẽ được sử dụng để thực hiện các thao tác phân quyền.

2.  **Gọi API phân quyền**:
    *   Để thiết lập quyền công khai, hàm `setPermissions` sẽ được gọi với `file_token` và một object chứa các cấu hình mong muốn.
    *   Để thêm thành viên, hàm `addPermissions` sẽ được gọi với `file_token` và thông tin của thành viên cần thêm.
    *   Để chuyển quyền sở hữu, hàm `transferOwnership` sẽ được gọi với `file_token` và email của người dùng mới.

3.  **Xử lý hàng loạt**: Hệ thống cũng hỗ trợ việc phân quyền cho nhiều file cùng một lúc thông qua hàm `setMultiplePermissions`, giúp tối ưu hóa hiệu suất khi cần phân quyền cho một số lượng lớn file.

## 5. Sơ đồ luồng hoạt động

Dưới đây là sơ đồ sequence diagram minh họa cho toàn bộ quy trình upload và phân quyền:

```mermaid
sequenceDiagram
    participant User as Người dùng
    participant System as Hệ thống
    participant LarkDriveAPI as Lark Drive API

    User->>System: Yêu cầu upload file
    System->>System: Kiểm tra cấu trúc thư mục
    alt Thư mục chưa tồn tại
        System->>LarkDriveAPI: createFolder()
        LarkDriveAPI-->>System: Trả về folder_token
    end

    alt File nhỏ (< 20MB)
        System->>LarkDriveAPI: upload_all(file, folder_token)
        LarkDriveAPI-->>System: Trả về file_token
    else File lớn (>= 20MB)
        System->>LarkDriveAPI: upload_prepare()
        LarkDriveAPI-->>System: Trả về upload_id
        loop Upload từng chunk
            System->>LarkDriveAPI: upload_part(chunk, upload_id)
        end
        System->>LarkDriveAPI: upload_finish(upload_id)
        LarkDriveAPI-->>System: Trả về file_token
    end

    System->>System: Lấy file_token đã lưu
    alt Phân quyền công khai
        System->>LarkDriveAPI: setPermissions(file_token, config)
    end
    alt Thêm thành viên
        System->>LarkDriveAPI: addPermissions(file_token, member_info)
    end
    alt Chuyển quyền sở hữu
        System->>LarkDriveAPI: transferOwnership(file_token, new_owner)
    end
    System-->>User: Hoàn tất
```
