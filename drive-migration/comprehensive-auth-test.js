console.log('🚀 Comprehensive Google Auth Test starting...');

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

import('./src/auth/google-auth.js')
    .then(async (module) => {
        console.log('✅ Module imported successfully');
        const { googleAuth } = module;

        try {
            // Use admin email from configuration
            const adminEmail = googleAuth.getAdminEmail();
            const domain = googleAuth.getDomain();

            console.log(`📋 Configuration: Domain=${domain}, Admin=${adminEmail}`);

            // Test 1: Admin Directory API
            console.log('\n📋 Test 1: Admin Directory API');
            console.log('🔄 Creating admin client...');

            const adminClient = await googleAuth.getAdminClient(adminEmail);
            console.log('✅ Admin client created');

            console.log('🌐 Testing users.list API...');
            const usersResult = await adminClient.users.list({
                domain: domain,
                maxResults: 3
            });

            console.log(`✅ Users API successful! Found ${usersResult.data.users?.length || 0} users`);
            if (usersResult.data.users && usersResult.data.users.length > 0) {
                console.log('👥 Sample users:');
                usersResult.data.users.forEach(user => {
                    console.log(`   - ${user.primaryEmail}`);
                });
            }

            // Test 2: Google Drive API with domain-wide delegation
            console.log('\n📂 Test 2: Google Drive API (Domain-wide Delegation)');
            console.log('🔄 Creating Drive client...');

            const driveClient = await googleAuth.getDriveClient(adminEmail);
            console.log('✅ Drive client created');

            console.log('🔍 Testing Drive about API...');
            const aboutResult = await driveClient.about.get({
                fields: 'user,storageQuota'
            });

            console.log('✅ Drive API successful!');
            console.log(`📧 Connected as: ${aboutResult.data.user.emailAddress}`);
            console.log(`💾 Storage used: ${aboutResult.data.storageQuota?.usage || 'N/A'} bytes`);

            // Test 3: Domain-wide delegation verification
            console.log('\n🔐 Test 3: Domain-wide Delegation Verification');

            if (aboutResult.data.user.emailAddress === adminEmail) {
                console.log('✅ Domain-wide delegation is working correctly');
                console.log(`   Successfully impersonating: ${adminEmail}`);
            } else {
                console.log('⚠️  Domain-wide delegation may have issues');
                console.log(`   Expected: ${adminEmail}`);
                console.log(`   Actual: ${aboutResult.data.user.emailAddress}`);
            }

            // Test 4: Error handling and auth validation
            console.log('\n🛡️  Test 4: Authentication Validation');

            const testResult = await googleAuth.testConnection(adminEmail);
            console.log(`✅ Connection test completed`);
            console.log(`   Domain-wide delegation working: ${testResult.domainWideDelegationWorking}`);
            console.log(`   Connected as: ${testResult.connectedAs}`);

            console.log('\n🎉 ALL TESTS PASSED - Google SDK v128 is working correctly!');
            console.log('✨ Authentication and domain-wide delegation are properly configured');

        } catch (error) {
            console.error('\n❌ Test FAILED:', error.message);

            if (error.response?.data?.error) {
                console.error('📋 Error details:', error.response.data.error);
            }

            if (error.code === 403) {
                console.log('\n💡 Troubleshooting tips:');
                console.log('   1. Check domain-wide delegation is enabled for this service account');
                console.log('   2. Verify the service account has the correct scopes in Google Admin Console');
                console.log('   3. Ensure the admin email exists and has proper permissions');
            }
        }
    })
    .catch(error => {
        console.error('💥 Import failed:', error.message);
        console.error('   Make sure the auth module is available and properly configured');
    });
