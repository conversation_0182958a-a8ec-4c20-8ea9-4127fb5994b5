/**
 * Test script để kiểm tra tính năng download
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';

const API_BASE = 'http://localhost:3000/api';
const TEST_DOWNLOAD_PATH = './temp/test-downloads';

async function testDownloadFeature() {
    console.log('🧪 Testing Download Feature...\n');

    try {
        // 1. Test API endpoints
        console.log('1️⃣ Testing API endpoints...');
        
        // Test get users
        console.log('   📋 Getting users...');
        const usersResponse = await axios.get(`${API_BASE}/download/users`);
        console.log(`   ✅ Found ${usersResponse.data.data.length} users`);
        
        const users = usersResponse.data.data;
        const usersWithFiles = users.filter(user => user.fileCount > 0);
        
        if (usersWithFiles.length === 0) {
            console.log('   ⚠️ No users with files found. Cannot test download.');
            return;
        }
        
        console.log(`   📁 Found ${usersWithFiles.length} users with files`);
        
        // Test get sessions
        console.log('   📋 Getting existing sessions...');
        const sessionsResponse = await axios.get(`${API_BASE}/download/sessions`);
        console.log(`   ✅ Found ${sessionsResponse.data.data.length} existing sessions`);

        // 2. Create test download session
        console.log('\n2️⃣ Creating test download session...');
        
        // Tạo thư mục test nếu chưa tồn tại
        if (!fs.existsSync(TEST_DOWNLOAD_PATH)) {
            fs.mkdirSync(TEST_DOWNLOAD_PATH, { recursive: true });
        }
        
        // Chọn user đầu tiên có files
        const testUser = usersWithFiles[0];
        console.log(`   👤 Selected user: ${testUser.primary_email} (${testUser.fileCount} files)`);
        
        const sessionConfig = {
            name: `Test Download Session - ${new Date().toISOString()}`,
            selectedUsers: [testUser.primary_email],
            downloadPath: path.resolve(TEST_DOWNLOAD_PATH),
            concurrentDownloads: 2,
            maxRetries: 2
        };
        
        console.log('   📝 Session config:', JSON.stringify(sessionConfig, null, 2));
        
        const createResponse = await axios.post(`${API_BASE}/download/sessions`, sessionConfig);
        
        if (!createResponse.data.success) {
            throw new Error(`Failed to create session: ${createResponse.data.error}`);
        }
        
        const session = createResponse.data.data;
        console.log(`   ✅ Created session: ${session.id}`);
        console.log(`   📊 Total files to download: ${session.total_files}`);

        // 3. Start download session
        console.log('\n3️⃣ Starting download session...');
        
        const startResponse = await axios.post(`${API_BASE}/download/sessions/${session.id}/start`);
        
        if (!startResponse.data.success) {
            throw new Error(`Failed to start session: ${startResponse.data.error}`);
        }
        
        console.log('   🚀 Download session started!');

        // 4. Monitor progress
        console.log('\n4️⃣ Monitoring download progress...');
        
        let completed = false;
        let attempts = 0;
        const maxAttempts = 60; // 2 minutes max
        
        while (!completed && attempts < maxAttempts) {
            attempts++;
            
            try {
                const progressResponse = await axios.get(`${API_BASE}/download/sessions/${session.id}/progress`);
                const progressData = progressResponse.data.data;
                
                const sessionData = progressData.session;
                const progress = progressData.progress;
                
                console.log(`   📊 Progress: ${sessionData.progress_percentage || 0}% | ` +
                          `Downloaded: ${sessionData.downloaded_files || 0}/${sessionData.total_files || 0} | ` +
                          `Status: ${sessionData.status}`);
                
                if (progress.completed > 0) {
                    console.log(`   ✅ Completed: ${progress.completed}`);
                }
                if (progress.failed > 0) {
                    console.log(`   ❌ Failed: ${progress.failed}`);
                }
                if (progress.downloading > 0) {
                    console.log(`   📥 Downloading: ${progress.downloading}`);
                }
                
                if (sessionData.current_file_name) {
                    console.log(`   📄 Current file: ${sessionData.current_file_name}`);
                }
                
                // Check if completed
                if (sessionData.status === 'completed' || sessionData.status === 'failed') {
                    completed = true;
                    console.log(`   🏁 Session ${sessionData.status}!`);
                    break;
                }
                
                // Wait 2 seconds before next check
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.log(`   ⚠️ Error checking progress: ${error.message}`);
            }
        }

        // 5. Check final results
        console.log('\n5️⃣ Checking final results...');
        
        const finalProgressResponse = await axios.get(`${API_BASE}/download/sessions/${session.id}/progress`);
        const finalData = finalProgressResponse.data.data;
        
        console.log('   📊 Final Statistics:');
        console.log(`      Status: ${finalData.session.status}`);
        console.log(`      Total Files: ${finalData.session.total_files}`);
        console.log(`      Downloaded: ${finalData.session.downloaded_files}`);
        console.log(`      Failed: ${finalData.session.failed_files}`);
        console.log(`      Duration: ${finalData.session.duration_seconds}s`);
        
        // 6. Check downloaded files
        console.log('\n6️⃣ Checking downloaded files...');
        
        const userFolder = path.join(TEST_DOWNLOAD_PATH, testUser.primary_email);
        
        if (fs.existsSync(userFolder)) {
            const files = getAllFiles(userFolder);
            console.log(`   📁 Found ${files.length} downloaded files in ${userFolder}`);
            
            files.slice(0, 5).forEach(file => {
                const stats = fs.statSync(file);
                console.log(`      📄 ${path.relative(userFolder, file)} (${formatFileSize(stats.size)})`);
            });
            
            if (files.length > 5) {
                console.log(`      ... and ${files.length - 5} more files`);
            }
        } else {
            console.log(`   ⚠️ User folder not found: ${userFolder}`);
        }

        console.log('\n🎉 Download feature test completed!');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

// Helper functions
function getAllFiles(dir) {
    const files = [];
    
    function scanDir(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stats = fs.statSync(fullPath);
            
            if (stats.isDirectory()) {
                scanDir(fullPath);
            } else {
                files.push(fullPath);
            }
        }
    }
    
    if (fs.existsSync(dir)) {
        scanDir(dir);
    }
    
    return files;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run test
testDownloadFeature();
