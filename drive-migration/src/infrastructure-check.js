import dotenv from 'dotenv';

dotenv.config();

console.log('🏗️ Drive-to-Lark Migrator - Infrastructure Check\n');
console.log('='.repeat(70));

/**
 * Comprehensive infrastructure validation
 */
async function checkInfrastructure() {
    const results = {
        environment: { success: false, details: {} },
        database: { success: false, details: {} },
        googleAuth: { success: false, details: {} },
        larkAuth: { success: false, details: {} },
        googleAPI: { success: false, details: {} },
        larkAPI: { success: false, details: {} }
    };

    // 1. Environment Variables Check
    console.log('\n1. 🔧 Environment Variables Check');
    console.log('-'.repeat(50));

    const requiredEnvVars = [
        'LARK_APP_ID',
        'LARK_APP_SECRET',
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'SUPABASE_SERVICE_ROLE_KEY'
    ];

    let envSuccess = true;
    for (const envVar of requiredEnvVars) {
        const value = process.env[envVar];
        if (value) {
            console.log(`✅ ${envVar}: Set`);
            results.environment.details[envVar] = 'Set';
        } else {
            console.log(`❌ ${envVar}: Missing`);
            results.environment.details[envVar] = 'Missing';
            envSuccess = false;
        }
    }
    results.environment.success = envSuccess;

    // 2. Database Check
    console.log('\n2. 🗄️ Supabase Database Check');
    console.log('-'.repeat(50));

    try {
        const { supabaseClient } = await import('./database/supabase.js');

        // Test connection
        const connectionTest = await supabaseClient.testConnection();
        if (connectionTest.success) {
            console.log('✅ Database connection: Success');
            results.database.details.connection = 'Success';
        } else {
            console.log('❌ Database connection: Failed');
            results.database.details.connection = 'Failed';
        }

        // Test tables
        const tables = ['users', 'migration_tasks', 'migration_items', 'permission_mappings', 'migration_logs'];
        let tablesOk = true;

        for (const table of tables) {
            try {
                const client = supabaseClient.getServiceClient();
                const { data, error } = await client.from(table).select('*').limit(1);

                if (error) {
                    console.log(`❌ Table ${table}: ${error.message}`);
                    results.database.details[table] = 'Error';
                    tablesOk = false;
                } else {
                    console.log(`✅ Table ${table}: OK`);
                    results.database.details[table] = 'OK';
                }
            } catch (err) {
                console.log(`❌ Table ${table}: ${err.message}`);
                results.database.details[table] = 'Exception';
                tablesOk = false;
            }
        }

        results.database.success = connectionTest.success && tablesOk;

    } catch (error) {
        console.log('❌ Database module import failed:', error.message);
        results.database.success = false;
        results.database.details.error = error.message;
    }

    // 3. Google Authentication Check
    console.log('\n3. 🔐 Google Authentication Check');
    console.log('-'.repeat(50));

    try {
        const { googleAuth } = await import('./auth/google-auth.js');

        const isValid = googleAuth.validateCredentials();
        if (isValid) {
            console.log('✅ Google credentials: Valid');
            console.log(`📧 Service Account: ${googleAuth.serviceAccountEmail}`);
            results.googleAuth.details.credentials = 'Valid';
            results.googleAuth.details.serviceAccount = googleAuth.serviceAccountEmail;
        } else {
            console.log('❌ Google credentials: Invalid');
            results.googleAuth.details.credentials = 'Invalid';
        }

        results.googleAuth.success = isValid;

    } catch (error) {
        console.log('❌ Google Auth module failed:', error.message);
        results.googleAuth.success = false;
        results.googleAuth.details.error = error.message;
    }

    // 4. Lark Authentication Check
    console.log('\n4. 🦄 Lark Authentication Check');
    console.log('-'.repeat(50));

    try {
        const { larkAuth } = await import('./auth/lark-auth.js');

        const isValid = larkAuth.validateCredentials();
        if (isValid) {
            console.log('✅ Lark credentials: Valid');
            console.log(`📱 App ID: ${larkAuth.appId}`);
            results.larkAuth.details.credentials = 'Valid';
            results.larkAuth.details.appId = larkAuth.appId;

            // Test token acquisition
            try {
                const token = await larkAuth.getTenantAccessToken();
                console.log('✅ Token acquisition: Success');
                console.log(`🎫 Token length: ${token.length} characters`);
                results.larkAuth.details.tokenAcquisition = 'Success';
                results.larkAuth.details.tokenLength = token.length;
            } catch (tokenError) {
                console.log('❌ Token acquisition: Failed');
                results.larkAuth.details.tokenAcquisition = 'Failed';
            }
        } else {
            console.log('❌ Lark credentials: Invalid');
            results.larkAuth.details.credentials = 'Invalid';
        }

        results.larkAuth.success = isValid;

    } catch (error) {
        console.log('❌ Lark Auth module failed:', error.message);
        results.larkAuth.success = false;
        results.larkAuth.details.error = error.message;
    }

    // 5. Google Drive API Check
    console.log('\n5. 📁 Google Drive API Check');
    console.log('-'.repeat(50));

    try {
        const { googleDriveAPI } = await import('./api/google-drive-api.js');
        const testEmail = process.argv[2]; // Get test email from command line

        if (testEmail) {
            const apiTest = await googleDriveAPI.testAPIs(testEmail);
            if (apiTest.success) {
                console.log('✅ Google Drive API: Success');
                console.log(`📊 Performance: ${JSON.stringify(apiTest.performance)}`);
                results.googleAPI.success = true;
                results.googleAPI.details = apiTest;
            } else {
                console.log('❌ Google Drive API: Failed');
                console.log(`❌ Errors: ${apiTest.errors.join(', ')}`);
                results.googleAPI.success = false;
                results.googleAPI.details = apiTest;
            }
        } else {
            console.log('⚠️ Google Drive API: Skipped (no test email provided)');
            console.log('💡 Usage: node infrastructure-check.js <EMAIL>');
            results.googleAPI.success = false;
            results.googleAPI.details.error = 'No test email provided';
        }

    } catch (error) {
        console.log('❌ Google Drive API module failed:', error.message);
        results.googleAPI.success = false;
        results.googleAPI.details.error = error.message;
    }

    // 6. Lark Drive API Check
    console.log('\n6. 🚀 Lark Drive API Check');
    console.log('-'.repeat(50));

    try {
        const { larkDriveAPI } = await import('./api/lark-drive-api.js');

        const apiTest = await larkDriveAPI.testAPIs();
        if (apiTest.success) {
            console.log('✅ Lark Drive API: Success');
            console.log(`📊 Performance: ${JSON.stringify(apiTest.performance)}`);
            results.larkAPI.success = true;
            results.larkAPI.details = apiTest;
        } else {
            console.log('❌ Lark Drive API: Failed');
            console.log(`❌ Errors: ${apiTest.errors.join(', ')}`);
            results.larkAPI.success = false;
            results.larkAPI.details = apiTest;
        }

    } catch (error) {
        console.log('❌ Lark Drive API module failed:', error.message);
        results.larkAPI.success = false;
        results.larkAPI.details.error = error.message;
    }

    // Summary
    console.log('\n' + '='.repeat(70));
    console.log('📊 INFRASTRUCTURE CHECK SUMMARY');
    console.log('='.repeat(70));

    const components = [
        ['Environment Variables', results.environment.success],
        ['Supabase Database', results.database.success],
        ['Google Authentication', results.googleAuth.success],
        ['Lark Authentication', results.larkAuth.success],
        ['Google Drive API', results.googleAPI.success],
        ['Lark Drive API', results.larkAPI.success]
    ];

    let overallSuccess = true;
    for (const [component, success] of components) {
        const status = success ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${component}`);
        if (!success) overallSuccess = false;
    }

    console.log('\n' + '='.repeat(70));
    if (overallSuccess) {
        console.log('🎉 ALL INFRASTRUCTURE COMPONENTS ARE WORKING!');
        console.log('✅ Ready for Sprint 2 implementation');
    } else {
        console.log('⚠️ SOME INFRASTRUCTURE ISSUES DETECTED');
        console.log('🔧 Please fix the failed components before proceeding');
    }
    console.log('='.repeat(70));

    return results;
}

// Run check
checkInfrastructure().catch(error => {
    console.error('❌ Infrastructure check failed:', error);
    process.exit(1);
});
