import express from 'express';
import { userMappingService } from '../services/user-mapping-service.js';
import { supabaseClient } from '../database/supabase.js';

const router = express.Router();

/**
 * Initialize user mapping from Drive permissions
 * POST /api/user-mapping/initialize
 */
router.post('/initialize', async (req, res) => {
    try {
        const { drivePermissions } = req.body;

        if (!Array.isArray(drivePermissions)) {
            return res.status(400).json({
                error: 'drivePermissions must be an array'
            });
        }

        console.log(`👥 Initializing user mapping from ${drivePermissions.length} permissions`);

        const result = await userMappingService.initializeUserMapping(drivePermissions);

        res.json({
            success: true,
            message: 'User mapping initialized successfully',
            ...result
        });

    } catch (error) {
        console.error('❌ Error initializing user mapping:', error.message);
        res.status(500).json({
            error: 'Failed to initialize user mapping',
            details: error.message
        });
    }
});

/**
 * Get all user mappings với pagination và filtering
 * GET /api/user-mapping/users
 */
router.get('/users', async (req, res) => {
    try {
        const {
            page = 1,
            pageSize = 50,
            search = '',
            mapped = 'all', // 'all', 'mapped', 'unmapped'
            sortBy = 'created_at',
            sortOrder = 'desc'
        } = req.query;

        // Build query
        let query = supabaseClient.getServiceClient()
            .from('users')
            .select('*', { count: 'exact' });

        // Apply filters
        if (search) {
            query = query.ilike('email_google', `%${search}%`);
        }

        if (mapped === 'mapped') {
            query = query.eq('mapped', true);
        } else if (mapped === 'unmapped') {
            query = query.eq('mapped', false);
        }

        // Apply sorting
        const ascending = sortOrder === 'asc';
        query = query.order(sortBy, { ascending });

        // Apply pagination
        const offset = (parseInt(page) - 1) * parseInt(pageSize);
        query = query.range(offset, offset + parseInt(pageSize) - 1);

        const { data: users, error, count } = await query;

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        const totalPages = Math.ceil((count || 0) / parseInt(pageSize));

        res.json({
            users: users || [],
            pagination: {
                currentPage: parseInt(page),
                pageSize: parseInt(pageSize),
                totalCount: count || 0,
                totalPages,
                hasNextPage: parseInt(page) < totalPages,
                hasPreviousPage: parseInt(page) > 1
            },
            filters: {
                search,
                mapped,
                sortBy,
                sortOrder
            }
        });

    } catch (error) {
        console.error('❌ Error getting user mappings:', error.message);
        res.status(500).json({
            error: 'Failed to get user mappings',
            details: error.message
        });
    }
});

/**
 * Update user mapping manually
 * PUT /api/user-mapping/users/:email
 */
router.put('/users/:email', async (req, res) => {
    try {
        const { email } = req.params;
        const { larkUserId, notes } = req.body;

        if (!larkUserId) {
            return res.status(400).json({
                error: 'larkUserId is required'
            });
        }

        console.log(`👤 Updating user mapping: ${email} -> ${larkUserId}`);

        const result = await userMappingService.updateUserMapping(
            email,
            larkUserId,
            true,
            notes || 'Manual mapping'
        );

        if (result.success) {
            res.json({
                success: true,
                message: 'User mapping updated successfully',
                user: result.updatedUser
            });
        } else {
            res.status(400).json({
                error: 'Failed to update user mapping',
                details: result.error
            });
        }

    } catch (error) {
        console.error('❌ Error updating user mapping:', error.message);
        res.status(500).json({
            error: 'Failed to update user mapping',
            details: error.message
        });
    }
});

/**
 * Delete user mapping
 * DELETE /api/user-mapping/users/:email
 */
router.delete('/users/:email', async (req, res) => {
    try {
        const { email } = req.params;

        console.log(`🗑️ Deleting user mapping: ${email}`);

        const { error } = await supabaseClient.getServiceClient()
            .from('users')
            .delete()
            .eq('email_google', email);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        res.json({
            success: true,
            message: 'User mapping deleted successfully'
        });

    } catch (error) {
        console.error('❌ Error deleting user mapping:', error.message);
        res.status(500).json({
            error: 'Failed to delete user mapping',
            details: error.message
        });
    }
});

/**
 * Perform auto-mapping for unmapped users
 * POST /api/user-mapping/auto-map
 */
router.post('/auto-map', async (req, res) => {
    try {
        console.log('🤖 Starting auto-mapping process...');

        // Get unmapped users
        const { data: unmappedUsers, error } = await supabaseClient.getServiceClient()
            .from('users')
            .select('*')
            .eq('mapped', false);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        if (!unmappedUsers || unmappedUsers.length === 0) {
            return res.json({
                success: true,
                message: 'No unmapped users found',
                autoMappedCount: 0
            });
        }

        // Convert to format expected by auto-mapping
        const usersForMapping = unmappedUsers.map(user => ({
            email: user.email_google,
            displayName: '', // We don't have this stored
            domain: user.email_google.split('@')[1]
        }));

        const result = await userMappingService.performAutoMapping(usersForMapping);

        // Update statistics
        await userMappingService.updateStatistics();

        res.json({
            success: true,
            message: `Auto-mapping completed: ${result.autoMappedCount} users mapped`,
            autoMappedCount: result.autoMappedCount,
            mappingResults: result.mappingResults
        });

    } catch (error) {
        console.error('❌ Error in auto-mapping:', error.message);
        res.status(500).json({
            error: 'Failed to perform auto-mapping',
            details: error.message
        });
    }
});

/**
 * Get user mapping statistics
 * GET /api/user-mapping/stats
 */
router.get('/stats', async (req, res) => {
    try {
        const stats = await userMappingService.updateStatistics();

        res.json({
            success: true,
            stats
        });

    } catch (error) {
        console.error('❌ Error getting user mapping stats:', error.message);
        res.status(500).json({
            error: 'Failed to get user mapping statistics',
            details: error.message
        });
    }
});

/**
 * Get available Lark users for mapping
 * GET /api/user-mapping/lark-users
 */
router.get('/lark-users', async (req, res) => {
    try {
        const { search = '' } = req.query;

        console.log('📞 Getting Lark users for mapping...');

        const larkUsers = await userMappingService.getLarkUsers();
        
        // Filter by search if provided
        let filteredUsers = larkUsers;
        if (search) {
            const searchLower = search.toLowerCase();
            filteredUsers = larkUsers.filter(user => 
                user.name?.toLowerCase().includes(searchLower) ||
                user.email?.toLowerCase().includes(searchLower) ||
                user.user_id?.toLowerCase().includes(searchLower)
            );
        }

        res.json({
            success: true,
            users: filteredUsers,
            totalCount: filteredUsers.length
        });

    } catch (error) {
        console.error('❌ Error getting Lark users:', error.message);
        res.status(500).json({
            error: 'Failed to get Lark users',
            details: error.message
        });
    }
});

/**
 * Bulk update user mappings
 * POST /api/user-mapping/bulk-update
 */
router.post('/bulk-update', async (req, res) => {
    try {
        const { mappings } = req.body;

        if (!Array.isArray(mappings)) {
            return res.status(400).json({
                error: 'mappings must be an array'
            });
        }

        console.log(`📦 Bulk updating ${mappings.length} user mappings`);

        const results = [];
        let successCount = 0;
        let errorCount = 0;

        for (const mapping of mappings) {
            try {
                const result = await userMappingService.updateUserMapping(
                    mapping.googleEmail,
                    mapping.larkUserId,
                    true,
                    mapping.notes || 'Bulk update'
                );

                if (result.success) {
                    successCount++;
                } else {
                    errorCount++;
                }

                results.push({
                    googleEmail: mapping.googleEmail,
                    success: result.success,
                    error: result.error || null
                });

            } catch (error) {
                errorCount++;
                results.push({
                    googleEmail: mapping.googleEmail,
                    success: false,
                    error: error.message
                });
            }
        }

        // Update statistics
        await userMappingService.updateStatistics();

        res.json({
            success: true,
            message: `Bulk update completed: ${successCount} success, ${errorCount} failed`,
            successCount,
            errorCount,
            results
        });

    } catch (error) {
        console.error('❌ Error in bulk update:', error.message);
        res.status(500).json({
            error: 'Failed to perform bulk update',
            details: error.message
        });
    }
});

/**
 * Clear user mapping cache
 * POST /api/user-mapping/clear-cache
 */
router.post('/clear-cache', async (req, res) => {
    try {
        userMappingService.clearCache();

        res.json({
            success: true,
            message: 'User mapping cache cleared successfully'
        });

    } catch (error) {
        console.error('❌ Error clearing cache:', error.message);
        res.status(500).json({
            error: 'Failed to clear cache',
            details: error.message
        });
    }
});

export default router;
