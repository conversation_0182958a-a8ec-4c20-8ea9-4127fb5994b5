import { googleDriveAPI } from '../api/google-drive-api.js';

/**
 * Path Resolver Service
 * Resolves folder paths to Google Drive folder IDs and vice versa
 */
export class PathResolver {
    constructor() {
        this.driveAPI = googleDriveAPI;
        
        // Cache for path-to-ID mappings
        this.pathToIdCache = new Map();
        this.idToPathCache = new Map();
        
        // Cache for folder hierarchy
        this.folderHierarchy = new Map();
        
        // Cache TTL (5 minutes)
        this.cacheTTL = 5 * 60 * 1000;
        this.cacheTimestamps = new Map();
    }

    /**
     * Resolve folder path to folder ID
     * @param {string} userEmail - User email
     * @param {string} folderPath - Folder path (e.g., "/Documents/Projects")
     * @returns {Promise<string>} Folder ID
     */
    async resolvePath(userEmail, folderPath) {
        try {
            // Normalize path
            const normalizedPath = this.normalizePath(folderPath);
            
            // Check cache first
            const cacheKey = `${userEmail}:${normalizedPath}`;
            if (this.isCacheValid(cacheKey)) {
                const cachedId = this.pathToIdCache.get(cacheKey);
                console.log(`📁 Path resolved from cache: ${normalizedPath} -> ${cachedId}`);
                return cachedId;
            }

            // Root folder
            if (normalizedPath === '/' || normalizedPath === '') {
                this.updateCache(cacheKey, 'root', normalizedPath);
                return 'root';
            }

            // Split path into components
            const pathComponents = normalizedPath.split('/').filter(component => component.length > 0);
            
            // Traverse path from root
            let currentFolderId = 'root';
            let currentPath = '';

            for (const folderName of pathComponents) {
                currentPath += `/${folderName}`;
                const currentCacheKey = `${userEmail}:${currentPath}`;

                // Check if this path component is cached
                if (this.isCacheValid(currentCacheKey)) {
                    currentFolderId = this.pathToIdCache.get(currentCacheKey);
                    continue;
                }

                // Find folder in current directory
                const folderId = await this.findFolderInParent(userEmail, currentFolderId, folderName);
                
                if (!folderId) {
                    throw new Error(`Folder not found: ${folderName} in path ${currentPath}`);
                }

                // Update cache
                this.updateCache(currentCacheKey, folderId, currentPath);
                currentFolderId = folderId;
            }

            console.log(`📁 Path resolved: ${normalizedPath} -> ${currentFolderId}`);
            return currentFolderId;

        } catch (error) {
            console.error(`❌ Error resolving path ${folderPath}:`, error.message);
            throw new Error(`Failed to resolve path: ${error.message}`);
        }
    }

    /**
     * Resolve folder ID to full path
     * @param {string} userEmail - User email
     * @param {string} folderId - Folder ID
     * @returns {Promise<string>} Full folder path
     */
    async resolveId(userEmail, folderId) {
        try {
            // Check cache first
            const cacheKey = `${userEmail}:${folderId}`;
            if (this.isCacheValid(cacheKey)) {
                const cachedPath = this.idToPathCache.get(cacheKey);
                console.log(`📁 ID resolved from cache: ${folderId} -> ${cachedPath}`);
                return cachedPath;
            }

            // Root folder
            if (folderId === 'root') {
                this.updateCache(cacheKey, '/', folderId);
                return '/';
            }

            // Build path by traversing up the hierarchy
            const pathComponents = [];
            let currentFolderId = folderId;

            while (currentFolderId && currentFolderId !== 'root') {
                const folderInfo = await this.getFolderInfo(userEmail, currentFolderId);
                
                if (!folderInfo) {
                    throw new Error(`Folder not found: ${currentFolderId}`);
                }

                pathComponents.unshift(folderInfo.name);
                
                // Get parent folder ID
                const parents = folderInfo.parents || [];
                currentFolderId = parents.length > 0 ? parents[0] : null;

                // Prevent infinite loops
                if (pathComponents.length > 100) {
                    throw new Error('Path depth exceeded maximum limit (100 levels)');
                }
            }

            const fullPath = '/' + pathComponents.join('/');
            
            // Update cache
            this.updateCache(cacheKey, fullPath, folderId);
            
            console.log(`📁 ID resolved: ${folderId} -> ${fullPath}`);
            return fullPath;

        } catch (error) {
            console.error(`❌ Error resolving ID ${folderId}:`, error.message);
            throw new Error(`Failed to resolve folder ID: ${error.message}`);
        }
    }

    /**
     * Find folder by name in parent directory
     * @param {string} userEmail - User email
     * @param {string} parentId - Parent folder ID
     * @param {string} folderName - Folder name to find
     * @returns {Promise<string|null>} Folder ID or null if not found
     */
    async findFolderInParent(userEmail, parentId, folderName) {
        try {
            const query = `'${parentId}' in parents and mimeType='application/vnd.google-apps.folder' and name='${folderName}' and trashed=false`;
            
            const response = await this.driveAPI.listFiles(userEmail, {
                q: query,
                fields: 'files(id, name)',
                supportsAllDrives: true,
                includeItemsFromAllDrives: true,
                pageSize: 10
            });

            const folders = response.files || [];
            
            if (folders.length === 0) {
                return null;
            }

            if (folders.length > 1) {
                console.warn(`⚠️ Multiple folders found with name "${folderName}" in parent ${parentId}. Using first one.`);
            }

            return folders[0].id;

        } catch (error) {
            console.error(`❌ Error finding folder ${folderName} in parent ${parentId}:`, error.message);
            return null;
        }
    }

    /**
     * Get folder information
     * @param {string} userEmail - User email
     * @param {string} folderId - Folder ID
     * @returns {Promise<object|null>} Folder info or null if not found
     */
    async getFolderInfo(userEmail, folderId) {
        try {
            const response = await this.driveAPI.getFile(userEmail, folderId, {
                fields: 'id, name, parents, mimeType'
            });

            return response;

        } catch (error) {
            console.error(`❌ Error getting folder info for ${folderId}:`, error.message);
            return null;
        }
    }

    /**
     * List folders in a directory
     * @param {string} userEmail - User email
     * @param {string} parentId - Parent folder ID (default: 'root')
     * @returns {Promise<Array>} List of folders
     */
    async listFolders(userEmail, parentId = 'root') {
        try {
            const query = `'${parentId}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false`;
            
            const response = await this.driveAPI.listFiles(userEmail, {
                q: query,
                fields: 'files(id, name, createdTime, modifiedTime)',
                supportsAllDrives: true,
                includeItemsFromAllDrives: true,
                orderBy: 'name',
                pageSize: 100
            });

            return response.files || [];

        } catch (error) {
            console.error(`❌ Error listing folders in ${parentId}:`, error.message);
            throw new Error(`Failed to list folders: ${error.message}`);
        }
    }

    /**
     * Get folder tree structure
     * @param {string} userEmail - User email
     * @param {string} rootId - Root folder ID
     * @param {number} maxDepth - Maximum depth to traverse
     * @returns {Promise<object>} Folder tree
     */
    async getFolderTree(userEmail, rootId = 'root', maxDepth = 5) {
        try {
            const tree = await this.buildFolderTree(userEmail, rootId, 0, maxDepth);
            return tree;

        } catch (error) {
            console.error(`❌ Error building folder tree:`, error.message);
            throw new Error(`Failed to build folder tree: ${error.message}`);
        }
    }

    /**
     * Build folder tree recursively
     * @param {string} userEmail - User email
     * @param {string} folderId - Current folder ID
     * @param {number} currentDepth - Current depth
     * @param {number} maxDepth - Maximum depth
     * @returns {Promise<object>} Folder tree node
     */
    async buildFolderTree(userEmail, folderId, currentDepth, maxDepth) {
        if (currentDepth >= maxDepth) {
            return null;
        }

        const folderInfo = folderId === 'root' 
            ? { id: 'root', name: 'My Drive' }
            : await this.getFolderInfo(userEmail, folderId);

        if (!folderInfo) {
            return null;
        }

        const children = [];
        
        if (currentDepth < maxDepth - 1) {
            const subfolders = await this.listFolders(userEmail, folderId);
            
            for (const subfolder of subfolders) {
                const childTree = await this.buildFolderTree(userEmail, subfolder.id, currentDepth + 1, maxDepth);
                if (childTree) {
                    children.push(childTree);
                }
            }
        }

        return {
            id: folderInfo.id,
            name: folderInfo.name,
            depth: currentDepth,
            children: children,
            hasChildren: children.length > 0
        };
    }

    /**
     * Normalize folder path
     * @param {string} path - Raw path
     * @returns {string} Normalized path
     */
    normalizePath(path) {
        if (!path || path === '/') {
            return '/';
        }

        // Remove leading/trailing slashes and normalize
        let normalized = path.trim().replace(/\/+/g, '/');
        
        if (!normalized.startsWith('/')) {
            normalized = '/' + normalized;
        }
        
        if (normalized.endsWith('/') && normalized !== '/') {
            normalized = normalized.slice(0, -1);
        }

        return normalized;
    }

    /**
     * Update cache with TTL
     * @param {string} key - Cache key
     * @param {string} value - Cache value
     * @param {string} reverseKey - Reverse mapping key
     */
    updateCache(key, value, reverseKey) {
        const timestamp = Date.now();
        
        this.pathToIdCache.set(key, value);
        this.cacheTimestamps.set(key, timestamp);
        
        if (reverseKey) {
            const reverseMapKey = key.includes(':') 
                ? `${key.split(':')[0]}:${value}`
                : `${value}`;
            this.idToPathCache.set(reverseMapKey, reverseKey);
            this.cacheTimestamps.set(reverseMapKey, timestamp);
        }
    }

    /**
     * Check if cache entry is valid
     * @param {string} key - Cache key
     * @returns {boolean} Is cache valid
     */
    isCacheValid(key) {
        if (!this.pathToIdCache.has(key) && !this.idToPathCache.has(key)) {
            return false;
        }

        const timestamp = this.cacheTimestamps.get(key);
        if (!timestamp) {
            return false;
        }

        return (Date.now() - timestamp) < this.cacheTTL;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.pathToIdCache.clear();
        this.idToPathCache.clear();
        this.cacheTimestamps.clear();
        this.folderHierarchy.clear();
        console.log('🗑️ Path resolver cache cleared');
    }

    /**
     * Get cache statistics
     * @returns {object} Cache stats
     */
    getCacheStats() {
        return {
            pathToIdEntries: this.pathToIdCache.size,
            idToPathEntries: this.idToPathCache.size,
            totalEntries: this.pathToIdCache.size + this.idToPathCache.size,
            cacheTTL: this.cacheTTL
        };
    }
}

// Export singleton instance
export const pathResolver = new PathResolver();
