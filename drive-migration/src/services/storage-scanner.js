import { googleAuth } from "../auth/google-auth.js";
import { supabaseClient } from "../database/supabase.js";
import { google } from "googleapis";

/**
 * Storage Scanner Service
 * Service để scan thông tin dung lượng sử dụng của user trên Google Drive và Gmail
 */
export class StorageScanner {
    constructor() {
        this.auth = googleAuth;
        this.supabase = supabaseClient;
        
        // Statistics tracking
        this.stats = {
            scannedUsers: 0,
            errors: 0,
            totalStorageScanned: 0,
        };
    }

    /**
     * Scan storage information for all users
     * @param {string[]} userEmails - Array of user emails to scan
     * @param {boolean} forceRefresh - Force refresh even if data exists
     * @returns {Promise<object>} Scan results
     */
    async scanAllUsersStorage(userEmails, forceRefresh = false) {
        console.log(`📊 Starting storage scan for ${userEmails.length} users...`);
        
        const results = {
            success: 0,
            failed: 0,
            errors: [],
            totalUsers: userEmails.length,
            startTime: Date.now()
        };

        for (const email of userEmails) {
            try {
                console.log(`📧 Scanning storage for: ${email}`);
                
                // Check if we need to scan this user
                if (!forceRefresh) {
                    const existing = await this.getExistingStorageData(email);
                    if (existing && this.isRecentScan(existing.last_scanned_at)) {
                        console.log(`   ⏭️ Skipping ${email} - recent scan exists`);
                        results.success++;
                        continue;
                    }
                }

                const storageInfo = await this.scanUserStorage(email);
                await this.saveStorageData(email, storageInfo);
                
                results.success++;
                this.stats.scannedUsers++;
                
                console.log(`   ✅ Completed storage scan for ${email}`);
                
            } catch (error) {
                console.error(`   ❌ Failed to scan storage for ${email}:`, error.message);
                results.failed++;
                results.errors.push({
                    email,
                    error: error.message
                });
                
                // Save error to database
                await this.saveStorageError(email, error.message);
            }
        }

        const duration = Date.now() - results.startTime;
        console.log(`📊 Storage scan completed: ${results.success}/${results.totalUsers} successful in ${duration}ms`);
        
        return results;
    }

    /**
     * Scan storage information for a single user
     * @param {string} userEmail - User email
     * @returns {Promise<object>} Storage information
     */
    async scanUserStorage(userEmail) {
        try {
            // Get Drive client
            const drive = await this.auth.getDriveClient(userEmail);
            
            // Get storage quota information from Drive API
            const aboutResponse = await drive.about.get({
                fields: 'user,storageQuota'
            });

            const storageQuota = aboutResponse.data.storageQuota || {};
            const user = aboutResponse.data.user || {};

            // Parse storage information
            const storageInfo = {
                userEmail: user.emailAddress || userEmail,
                // Drive storage (includes Drive, Gmail, and Photos)
                totalUsage: parseInt(storageQuota.usage) || 0,
                totalLimit: parseInt(storageQuota.limit) || 0,
                // Drive-specific usage
                driveUsage: parseInt(storageQuota.usageInDrive) || 0,
                // Gmail usage (if available)
                gmailUsage: parseInt(storageQuota.usageInGmail) || 0,
                // Photos usage (if available) 
                photosUsage: parseInt(storageQuota.usageInPhotos) || 0,
                // Additional metadata
                scanTime: new Date().toISOString(),
                quotaExceeded: storageQuota.usage >= storageQuota.limit
            };

            console.log(`   📊 Storage info for ${userEmail}:`);
            console.log(`      Total: ${this.formatBytes(storageInfo.totalUsage)} / ${this.formatBytes(storageInfo.totalLimit)}`);
            console.log(`      Drive: ${this.formatBytes(storageInfo.driveUsage)}`);
            console.log(`      Gmail: ${this.formatBytes(storageInfo.gmailUsage)}`);

            return storageInfo;

        } catch (error) {
            console.error(`❌ Error scanning storage for ${userEmail}:`, error.message);
            throw new Error(`Failed to scan storage for ${userEmail}: ${error.message}`);
        }
    }

    /**
     * Get existing storage data from database
     * @param {string} userEmail - User email
     * @returns {Promise<object|null>} Existing storage data
     */
    async getExistingStorageData(userEmail) {
        try {
            const { data, error } = await this.supabase
                .getServiceClient()
                .from('user_storage_stats')
                .select('*')
                .eq('user_email', userEmail)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
                throw error;
            }

            return data;
        } catch (error) {
            console.error(`Error getting existing storage data for ${userEmail}:`, error.message);
            return null;
        }
    }

    /**
     * Save storage data to database
     * @param {string} userEmail - User email
     * @param {object} storageInfo - Storage information
     */
    async saveStorageData(userEmail, storageInfo) {
        try {
            const storageData = {
                user_email: userEmail,
                drive_usage_bytes: storageInfo.driveUsage,
                drive_limit_bytes: storageInfo.totalLimit, // Drive limit is same as total limit
                gmail_usage_bytes: storageInfo.gmailUsage,
                total_usage_bytes: storageInfo.totalUsage,
                total_limit_bytes: storageInfo.totalLimit,
                last_scanned_at: new Date().toISOString(),
                scan_error_message: null
            };

            const { error } = await this.supabase
                .getServiceClient()
                .from('user_storage_stats')
                .upsert(storageData, { 
                    onConflict: 'user_email',
                    ignoreDuplicates: false 
                });

            if (error) {
                throw error;
            }

            console.log(`   💾 Saved storage data for ${userEmail}`);

        } catch (error) {
            console.error(`Error saving storage data for ${userEmail}:`, error.message);
            throw error;
        }
    }

    /**
     * Save storage scan error to database
     * @param {string} userEmail - User email
     * @param {string} errorMessage - Error message
     */
    async saveStorageError(userEmail, errorMessage) {
        try {
            const errorData = {
                user_email: userEmail,
                last_scanned_at: new Date().toISOString(),
                scan_error_message: errorMessage
            };

            await this.supabase
                .getServiceClient()
                .from('user_storage_stats')
                .upsert(errorData, { 
                    onConflict: 'user_email',
                    ignoreDuplicates: false 
                });

        } catch (error) {
            console.error(`Error saving storage error for ${userEmail}:`, error.message);
        }
    }

    /**
     * Check if scan is recent (within last 24 hours)
     * @param {string} lastScannedAt - Last scan timestamp
     * @returns {boolean} True if recent
     */
    isRecentScan(lastScannedAt) {
        if (!lastScannedAt) return false;
        
        const lastScan = new Date(lastScannedAt);
        const now = new Date();
        const hoursDiff = (now - lastScan) / (1000 * 60 * 60);
        
        return hoursDiff < 24; // Consider recent if scanned within 24 hours
    }

    /**
     * Format bytes to human readable format
     * @param {number} bytes - Bytes
     * @returns {string} Formatted string
     */
    formatBytes(bytes) {
        if (!bytes || bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get storage statistics summary
     * @returns {Promise<object>} Storage statistics
     */
    async getStorageStatistics() {
        try {
            const { data, error } = await this.supabase
                .getServiceClient()
                .from('user_storage_stats')
                .select('*');

            if (error) {
                throw error;
            }

            const stats = {
                totalUsers: data.length,
                totalStorageUsed: data.reduce((sum, user) => sum + (user.total_usage_bytes || 0), 0),
                totalDriveUsed: data.reduce((sum, user) => sum + (user.drive_usage_bytes || 0), 0),
                totalGmailUsed: data.reduce((sum, user) => sum + (user.gmail_usage_bytes || 0), 0),
                usersWithErrors: data.filter(user => user.scan_error_message).length,
                lastScanned: data.reduce((latest, user) => {
                    const userScan = new Date(user.last_scanned_at || 0);
                    return userScan > latest ? userScan : latest;
                }, new Date(0))
            };

            return stats;

        } catch (error) {
            console.error('Error getting storage statistics:', error.message);
            throw error;
        }
    }
}

// Export singleton instance
export const storageScanner = new StorageScanner();
