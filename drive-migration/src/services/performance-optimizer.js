/**
 * Performance Optimizer - <PERSON><PERSON><PERSON> <PERSON>u hiệu suất migration
 * Mục tiêu: 500+ files/phút, xử lý file lớn hiệu quả
 */

import os from 'os';
import cluster from 'cluster';
import { Worker } from 'worker_threads';
import { supabaseClient } from '../database/supabase.js';

class PerformanceOptimizer {
    constructor() {
        this.cpuCount = os.cpus().length;
        this.maxConcurrentFiles = Math.min(this.cpuCount * 2, 20); // Limit to prevent overwhelming APIs
        this.batchSize = 50;
        this.targetFilesPerMinute = 500;
        this.maxFileSize = 15 * 1024 * 1024 * 1024; // 15GB limit
        
        // Performance metrics
        this.metrics = {
            filesProcessed: 0,
            totalBytes: 0,
            startTime: null,
            errors: 0,
            retries: 0,
            averageFileTime: 0,
            throughput: 0
        };

        // Connection pools
        this.connectionPools = {
            google: new Map(),
            lark: new Map()
        };

        console.log(`🚀 Performance Optimizer initialized:`);
        console.log(`   💻 CPU cores: ${this.cpuCount}`);
        console.log(`   🔄 Max concurrent files: ${this.maxConcurrentFiles}`);
        console.log(`   📦 Batch size: ${this.batchSize}`);
        console.log(`   🎯 Target: ${this.targetFilesPerMinute} files/minute`);
    }

    /**
     * Optimize migration performance
     */
    async optimizeMigration(migrationTaskId) {
        try {
            console.log(`⚡ Starting performance optimization for task ${migrationTaskId}`);
            
            this.metrics.startTime = Date.now();
            
            // Analyze migration task
            const analysis = await this.analyzeMigrationTask(migrationTaskId);
            if (!analysis.success) {
                throw new Error(analysis.error);
            }

            // Create optimization strategy
            const strategy = this.createOptimizationStrategy(analysis.data);
            
            console.log(`📊 Optimization strategy:`);
            console.log(`   📁 Total files: ${strategy.totalFiles}`);
            console.log(`   📏 Total size: ${this.formatBytes(strategy.totalSize)}`);
            console.log(`   🔄 Concurrent workers: ${strategy.concurrentWorkers}`);
            console.log(`   📦 Batch size: ${strategy.batchSize}`);
            console.log(`   ⏱️ Estimated time: ${strategy.estimatedTimeMinutes} minutes`);

            // Execute optimized migration
            const result = await this.executeOptimizedMigration(migrationTaskId, strategy);
            
            return result;
        } catch (error) {
            console.error('❌ Performance optimization failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Analyze migration task để tạo strategy
     */
    async analyzeMigrationTask(migrationTaskId) {
        try {
            // Get migration items
            const { data: items, error } = await supabaseClient.getServiceClient()
                .from('migration_items')
                .select('*')
                .eq('migration_task_id', migrationTaskId)
                .eq('status', 'pending');

            if (error) throw error;

            // Analyze file sizes và types
            const analysis = {
                totalFiles: items.length,
                totalSize: 0,
                fileSizeDistribution: {
                    small: 0,    // < 10MB
                    medium: 0,   // 10MB - 100MB
                    large: 0,    // 100MB - 1GB
                    xlarge: 0    // > 1GB
                },
                fileTypes: new Map(),
                averageFileSize: 0
            };

            items.forEach(item => {
                const size = item.google_file_size || 0;
                analysis.totalSize += size;

                // Size distribution
                if (size < 10 * 1024 * 1024) {
                    analysis.fileSizeDistribution.small++;
                } else if (size < 100 * 1024 * 1024) {
                    analysis.fileSizeDistribution.medium++;
                } else if (size < 1024 * 1024 * 1024) {
                    analysis.fileSizeDistribution.large++;
                } else {
                    analysis.fileSizeDistribution.xlarge++;
                }

                // File types
                const mimeType = item.file_type || 'unknown';
                analysis.fileTypes.set(mimeType, (analysis.fileTypes.get(mimeType) || 0) + 1);
            });

            analysis.averageFileSize = analysis.totalFiles > 0 ? 
                analysis.totalSize / analysis.totalFiles : 0;

            return {
                success: true,
                data: analysis
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create optimization strategy based on analysis
     */
    createOptimizationStrategy(analysis) {
        const strategy = {
            totalFiles: analysis.totalFiles,
            totalSize: analysis.totalSize,
            concurrentWorkers: this.maxConcurrentFiles,
            batchSize: this.batchSize,
            estimatedTimeMinutes: 0,
            prioritization: 'size_ascending', // Start with small files
            chunking: {
                enabled: true,
                chunkSize: 10 * 1024 * 1024, // 10MB chunks
                maxChunks: 100
            },
            caching: {
                enabled: true,
                maxCacheSize: 100 * 1024 * 1024 // 100MB cache
            },
            retryStrategy: {
                maxRetries: 3,
                backoffMultiplier: 2,
                initialDelay: 1000
            }
        };

        // Adjust strategy based on file size distribution
        if (analysis.fileSizeDistribution.xlarge > 0) {
            // Many large files - reduce concurrency, increase chunk size
            strategy.concurrentWorkers = Math.max(2, Math.floor(this.maxConcurrentFiles / 2));
            strategy.chunking.chunkSize = 50 * 1024 * 1024; // 50MB chunks
            strategy.prioritization = 'size_descending'; // Process large files first
        } else if (analysis.fileSizeDistribution.small > analysis.totalFiles * 0.8) {
            // Mostly small files - increase concurrency
            strategy.concurrentWorkers = this.maxConcurrentFiles;
            strategy.batchSize = Math.min(100, this.batchSize * 2);
        }

        // Estimate time based on target throughput
        const filesPerSecond = this.targetFilesPerMinute / 60;
        strategy.estimatedTimeMinutes = Math.ceil(analysis.totalFiles / filesPerSecond / 60);

        return strategy;
    }

    /**
     * Execute optimized migration với parallel processing
     */
    async executeOptimizedMigration(migrationTaskId, strategy) {
        try {
            console.log(`🚀 Starting optimized migration execution`);

            // Get pending items sorted by strategy
            const items = await this.getPrioritizedItems(migrationTaskId, strategy);
            
            // Create worker pool
            const workers = await this.createWorkerPool(strategy.concurrentWorkers);
            
            // Process items in batches
            const batches = this.createBatches(items, strategy.batchSize);
            let processedBatches = 0;
            
            const results = {
                success: true,
                totalFiles: items.length,
                processedFiles: 0,
                failedFiles: 0,
                totalBytes: 0,
                duration: 0,
                throughput: 0
            };

            console.log(`📦 Processing ${batches.length} batches with ${workers.length} workers`);

            // Process batches concurrently
            const batchPromises = batches.map(async (batch, batchIndex) => {
                const worker = workers[batchIndex % workers.length];
                
                try {
                    const batchResult = await this.processBatch(worker, batch, strategy);
                    
                    results.processedFiles += batchResult.processedFiles;
                    results.failedFiles += batchResult.failedFiles;
                    results.totalBytes += batchResult.totalBytes;
                    
                    processedBatches++;
                    const progress = (processedBatches / batches.length * 100).toFixed(1);
                    console.log(`📊 Batch ${batchIndex + 1}/${batches.length} completed (${progress}%)`);
                    
                    return batchResult;
                } catch (error) {
                    console.error(`❌ Batch ${batchIndex + 1} failed:`, error);
                    results.failedFiles += batch.length;
                    return { error: error.message };
                }
            });

            // Wait for all batches to complete
            await Promise.all(batchPromises);
            
            // Calculate final metrics
            results.duration = Date.now() - this.metrics.startTime;
            results.throughput = results.processedFiles / (results.duration / 60000); // files per minute

            // Cleanup workers
            await this.cleanupWorkers(workers);

            console.log(`✅ Optimized migration completed:`);
            console.log(`   📁 Processed: ${results.processedFiles}/${results.totalFiles} files`);
            console.log(`   ❌ Failed: ${results.failedFiles} files`);
            console.log(`   📏 Total size: ${this.formatBytes(results.totalBytes)}`);
            console.log(`   ⏱️ Duration: ${(results.duration / 1000).toFixed(1)} seconds`);
            console.log(`   🚀 Throughput: ${results.throughput.toFixed(1)} files/minute`);

            return results;
        } catch (error) {
            console.error('❌ Optimized migration execution failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get prioritized items based on strategy
     */
    async getPrioritizedItems(migrationTaskId, strategy) {
        try {
            let orderBy = 'created_at';
            let ascending = true;

            if (strategy.prioritization === 'size_ascending') {
                orderBy = 'google_file_size';
                ascending = true;
            } else if (strategy.prioritization === 'size_descending') {
                orderBy = 'google_file_size';
                ascending = false;
            }

            const { data: items, error } = await supabaseClient.getServiceClient()
                .from('migration_items')
                .select('*')
                .eq('migration_task_id', migrationTaskId)
                .eq('status', 'pending')
                .order(orderBy, { ascending });

            if (error) throw error;

            return items;
        } catch (error) {
            console.error('Error getting prioritized items:', error);
            return [];
        }
    }

    /**
     * Create worker pool for parallel processing
     */
    async createWorkerPool(workerCount) {
        const workers = [];
        
        for (let i = 0; i < workerCount; i++) {
            // In a real implementation, these would be actual worker threads
            // For now, we'll simulate with worker objects
            workers.push({
                id: i,
                busy: false,
                processedFiles: 0
            });
        }

        console.log(`👥 Created worker pool with ${workers.length} workers`);
        return workers;
    }

    /**
     * Process a batch of files
     */
    async processBatch(worker, batch, strategy) {
        worker.busy = true;
        
        const batchResult = {
            processedFiles: 0,
            failedFiles: 0,
            totalBytes: 0
        };

        try {
            for (const item of batch) {
                try {
                    // Simulate file processing
                    const fileSize = item.google_file_size || 0;
                    const processingTime = this.estimateProcessingTime(fileSize);
                    
                    // Simulate processing delay
                    await new Promise(resolve => setTimeout(resolve, Math.min(processingTime, 100)));
                    
                    batchResult.processedFiles++;
                    batchResult.totalBytes += fileSize;
                    worker.processedFiles++;
                    
                } catch (error) {
                    console.error(`❌ Failed to process file ${item.google_file_id}:`, error);
                    batchResult.failedFiles++;
                }
            }
        } finally {
            worker.busy = false;
        }

        return batchResult;
    }

    /**
     * Create batches from items array
     */
    createBatches(items, batchSize) {
        const batches = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }

    /**
     * Estimate processing time for a file
     */
    estimateProcessingTime(fileSize) {
        // Base time + size-dependent time
        const baseTime = 1000; // 1 second base
        const sizeTime = fileSize / (1024 * 1024) * 100; // 100ms per MB
        return baseTime + sizeTime;
    }

    /**
     * Cleanup worker pool
     */
    async cleanupWorkers(workers) {
        console.log(`🧹 Cleaning up ${workers.length} workers`);
        // In real implementation, terminate worker threads
        workers.forEach(worker => {
            worker.busy = false;
        });
    }

    /**
     * Format bytes to human readable
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get performance metrics
     */
    getMetrics() {
        const duration = this.metrics.startTime ? Date.now() - this.metrics.startTime : 0;
        const throughput = duration > 0 ? (this.metrics.filesProcessed / (duration / 60000)) : 0;

        return {
            ...this.metrics,
            duration,
            throughput,
            averageFileSize: this.metrics.filesProcessed > 0 ? 
                this.metrics.totalBytes / this.metrics.filesProcessed : 0
        };
    }

    /**
     * Monitor system resources
     */
    getSystemMetrics() {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        return {
            memory: {
                used: memUsage.heapUsed,
                total: memUsage.heapTotal,
                external: memUsage.external,
                rss: memUsage.rss
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            },
            uptime: process.uptime(),
            loadAverage: os.loadavg()
        };
    }
}

export default PerformanceOptimizer;
