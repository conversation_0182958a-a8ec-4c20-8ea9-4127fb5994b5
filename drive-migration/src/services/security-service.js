/**
 * Security Service - T<PERSON><PERSON> cường bảo mật cho hệ thống
 * <PERSON><PERSON> hóa credentials, quản lý secrets, audit logs
 */

import crypto from 'crypto';
import { supabaseClient } from '../database/supabase.js';

class SecurityService {
    constructor() {
        this.algorithm = 'aes-256-gcm';
        this.keyLength = 32;
        this.ivLength = 16;
        this.tagLength = 16;
        
        // Master key từ environment variable
        this.masterKey = this.deriveMasterKey();
    }

    /**
     * Derive master key từ environment variables
     */
    deriveMasterKey() {
        const secret = process.env.ENCRYPTION_SECRET || 'default-secret-key-change-in-production';
        const salt = process.env.ENCRYPTION_SALT || 'default-salt';
        
        return crypto.pbkdf2Sync(secret, salt, 100000, this.keyLength, 'sha256');
    }

    /**
     * Mã hóa dữ liệu nh<PERSON>y cảm
     */
    encrypt(plaintext) {
        try {
            const iv = crypto.randomBytes(this.ivLength);
            const cipher = crypto.createCipher(this.algorithm, this.masterKey, { iv });
            
            let encrypted = cipher.update(plaintext, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const tag = cipher.getAuthTag();
            
            // Combine iv + tag + encrypted data
            const result = iv.toString('hex') + tag.toString('hex') + encrypted;
            
            return {
                success: true,
                data: result
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Giải mã dữ liệu
     */
    decrypt(encryptedData) {
        try {
            // Extract iv, tag, and encrypted data
            const iv = Buffer.from(encryptedData.slice(0, this.ivLength * 2), 'hex');
            const tag = Buffer.from(encryptedData.slice(this.ivLength * 2, (this.ivLength + this.tagLength) * 2), 'hex');
            const encrypted = encryptedData.slice((this.ivLength + this.tagLength) * 2);
            
            const decipher = crypto.createDecipher(this.algorithm, this.masterKey, { iv });
            decipher.setAuthTag(tag);
            
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return {
                success: true,
                data: decrypted
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Mã hóa Google Service Account credentials
     */
    async encryptGoogleCredentials(serviceAccountJson) {
        try {
            const credentialsString = JSON.stringify(serviceAccountJson);
            const encryptResult = this.encrypt(credentialsString);
            
            if (!encryptResult.success) {
                throw new Error(encryptResult.error);
            }

            // Store encrypted credentials in Supabase Vault (simulated)
            const vaultKey = `google_credentials_${Date.now()}`;
            const storeResult = await this.storeInVault(vaultKey, encryptResult.data);
            
            if (!storeResult.success) {
                throw new Error(storeResult.error);
            }

            return {
                success: true,
                vaultKey,
                message: 'Google credentials encrypted and stored securely'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Giải mã Google Service Account credentials
     */
    async decryptGoogleCredentials(vaultKey) {
        try {
            const retrieveResult = await this.retrieveFromVault(vaultKey);
            
            if (!retrieveResult.success) {
                throw new Error(retrieveResult.error);
            }

            const decryptResult = this.decrypt(retrieveResult.data);
            
            if (!decryptResult.success) {
                throw new Error(decryptResult.error);
            }

            return {
                success: true,
                data: JSON.parse(decryptResult.data)
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Mã hóa Lark App credentials
     */
    async encryptLarkCredentials(appId, appSecret) {
        try {
            const credentials = { appId, appSecret };
            const credentialsString = JSON.stringify(credentials);
            const encryptResult = this.encrypt(credentialsString);
            
            if (!encryptResult.success) {
                throw new Error(encryptResult.error);
            }

            const vaultKey = `lark_credentials_${Date.now()}`;
            const storeResult = await this.storeInVault(vaultKey, encryptResult.data);
            
            if (!storeResult.success) {
                throw new Error(storeResult.error);
            }

            return {
                success: true,
                vaultKey,
                message: 'Lark credentials encrypted and stored securely'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Giải mã Lark App credentials
     */
    async decryptLarkCredentials(vaultKey) {
        try {
            const retrieveResult = await this.retrieveFromVault(vaultKey);
            
            if (!retrieveResult.success) {
                throw new Error(retrieveResult.error);
            }

            const decryptResult = this.decrypt(retrieveResult.data);
            
            if (!decryptResult.success) {
                throw new Error(decryptResult.error);
            }

            return {
                success: true,
                data: JSON.parse(decryptResult.data)
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Store encrypted data in Supabase Vault (simulated với custom table)
     */
    async storeInVault(key, encryptedData) {
        try {
            const { data, error } = await supabaseClient.getServiceClient()
                .from('secure_vault')
                .upsert([{
                    vault_key: key,
                    encrypted_data: encryptedData,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }])
                .select()
                .single();

            if (error) {
                // If table doesn't exist, create it
                if (error.code === '42P01') {
                    await this.createVaultTable();
                    return await this.storeInVault(key, encryptedData);
                }
                throw error;
            }

            return { success: true, data };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Retrieve encrypted data from Supabase Vault
     */
    async retrieveFromVault(key) {
        try {
            const { data, error } = await supabaseClient.getServiceClient()
                .from('secure_vault')
                .select('encrypted_data')
                .eq('vault_key', key)
                .single();

            if (error) throw error;

            return {
                success: true,
                data: data.encrypted_data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create vault table if it doesn't exist
     */
    async createVaultTable() {
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS secure_vault (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                vault_key TEXT UNIQUE NOT NULL,
                encrypted_data TEXT NOT NULL,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Enable RLS
            ALTER TABLE secure_vault ENABLE ROW LEVEL SECURITY;
            
            -- Only service role can access vault
            CREATE POLICY "Service role only access" ON secure_vault
                FOR ALL USING (auth.role() = 'service_role');
        `;

        try {
            // Note: In production, this should be done via Supabase dashboard
            console.log('📋 Vault table creation SQL ready. Please execute in Supabase dashboard:');
            console.log(createTableSQL);
            
            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Generate secure random token
     */
    generateSecureToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    /**
     * Hash password với salt
     */
    hashPassword(password, salt = null) {
        if (!salt) {
            salt = crypto.randomBytes(16).toString('hex');
        }
        
        const hash = crypto.pbkdf2Sync(password, salt, 100000, 64, 'sha256').toString('hex');
        
        return {
            hash,
            salt
        };
    }

    /**
     * Verify password
     */
    verifyPassword(password, hash, salt) {
        const verifyHash = crypto.pbkdf2Sync(password, salt, 100000, 64, 'sha256').toString('hex');
        return hash === verifyHash;
    }

    /**
     * Create audit log entry
     */
    async createAuditLog(action, userId, details = {}) {
        try {
            const logEntry = {
                action,
                user_id: userId,
                details: JSON.stringify(details),
                ip_address: details.ip_address || 'unknown',
                user_agent: details.user_agent || 'unknown',
                timestamp: new Date().toISOString()
            };

            // Store in audit_logs table (would need to be created)
            console.log('🔍 Audit Log:', logEntry);
            
            return { success: true, data: logEntry };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Validate input để prevent injection attacks
     */
    sanitizeInput(input) {
        if (typeof input !== 'string') {
            return input;
        }
        
        // Remove potentially dangerous characters
        return input
            .replace(/[<>]/g, '') // Remove < and >
            .replace(/javascript:/gi, '') // Remove javascript: protocol
            .replace(/on\w+=/gi, '') // Remove event handlers
            .trim();
    }

    /**
     * Rate limiting check
     */
    async checkRateLimit(identifier, maxRequests = 100, windowMs = 3600000) {
        // Simple in-memory rate limiting (in production, use Redis)
        const now = Date.now();
        const windowStart = now - windowMs;
        
        // This would be stored in Redis or database in production
        const key = `rate_limit_${identifier}`;
        
        return {
            allowed: true,
            remaining: maxRequests - 1,
            resetTime: now + windowMs
        };
    }
}

export default SecurityService;
