import { larkAuth } from '../auth/lark-auth.js';
import FormData from 'form-data';
import fs from 'fs';

/**
 * Lark Drive API Client
 * Enhanced wrapper cho migration operations với comprehensive features
 */
export class LarkDriveAPI {
    constructor() {
        this.auth = larkAuth;

        // Statistics tracking
        this.stats = {
            apiCalls: 0,
            errors: 0,
            filesUploaded: 0,
            foldersCreated: 0,
            bytesUploaded: 0,
            permissionsSet: 0
        };

        // Cache for frequently accessed data
        this.cache = new Map();

        // Upload configuration
        this.uploadConfig = {
            chunkSize: 10 * 1024 * 1024, // 10MB chunks
            maxRetries: 3,
            retryDelay: 1000, // 1 second
            maxFileSize: 15 * 1024 * 1024 * 1024 // 15GB limit
        };

        // API endpoints
        this.endpoints = {
            rootFolderMeta: '/drive/explorer/v2/root_folder/meta',
            createFolder: '/drive/v1/files/create_folder',
            uploadPrepare: '/drive/v1/files/upload_prepare',
            uploadPart: '/drive/v1/files/upload_part',
            uploadFinish: '/drive/v1/files/upload_finish',
            uploadAll: '/drive/v1/files/upload_all',
            fileInfo: '/drive/v1/files',
            permissions: '/drive/v1/permissions',
            folderChildren: '/drive/v1/files',
            search: '/drive/v1/files/search'
        };
    }

    /**
     * Track API call statistics
     */
    trackApiCall(operation, success = true, bytes = 0) {
        this.stats.apiCalls++;
        if (!success) {
            this.stats.errors++;
        }

        switch (operation) {
            case 'upload':
                if (success) {
                    this.stats.filesUploaded++;
                    this.stats.bytesUploaded += bytes;
                }
                break;
            case 'createFolder':
                if (success) this.stats.foldersCreated++;
                break;
            case 'setPermissions':
                if (success) this.stats.permissionsSet++;
                break;
        }
    }

    /**
     * Tạo folder trên Lark Drive với enhanced features
     * @param {string} name - Tên folder
     * @param {string} parentFolderId - ID của parent folder (optional)
     * @param {object} options - Additional options
     * @returns {Promise<object>} Enhanced folder info
     */
    async createFolder(name, parentFolderId = null, options = {}) {
        try {
            const client = await this.auth.getAuthenticatedClient('drive');

            const requestBody = {
                name
            };

            if (!parentFolderId) {
                parentFolderId = await this.getRootFolderMeta();
            }

            requestBody.folder_token = parentFolderId;

            console.log(`📁 Creating folder: ${name} in ${parentFolderId}`);

            const startTime = Date.now();
            const response = await client.post(this.endpoints.createFolder, requestBody);
            const duration = Date.now() - startTime;

            if (response.data.code === 0) {
                const folderData = {
                    ...response.data.data,
                    name,
                    parentFolderId,
                    createdAt: new Date().toISOString(),
                    queryTime: duration
                };

                this.trackApiCall('createFolder', true);
                console.log(`✅ Folder created: ${folderData.token} in ${duration}ms`);
                console.log(`✅ Folder URL: ${folderData.url}`);

                // Cache folder info
                this.cache.set(`folder_${folderData.token}`, {
                    data: folderData,
                    expires: Date.now() + (30 * 60 * 1000) // 30 minutes
                });

                return folderData;
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            this.trackApiCall('createFolder', false);
            console.error(`❌ Error creating folder ${name}:`, error.message);
            throw new Error(`Failed to create folder ${name}: ${error.message}`);
        }
    }

    /**
     * Tạo folder hierarchy (nested folders)
     * @param {Array<string>} folderPath - Array of folder names
     * @param {string} parentFolderId - Starting parent folder ID
     * @returns {Promise<object>} Final folder info
     */
    async createFolderHierarchy(folderPath, parentFolderId = null) {
        try {
            let currentParent = parentFolderId;
            let createdFolders = [];

            console.log(`📁 Creating folder hierarchy: ${folderPath.join('/')}`);

            for (const folderName of folderPath) {
                // Check if folder already exists
                const existingFolder = await this.findFolderByName(folderName, currentParent);

                if (existingFolder) {
                    console.log(`📁 Folder already exists: ${folderName}`);
                    currentParent = existingFolder.token;
                    createdFolders.push(existingFolder);
                } else {
                    const newFolder = await this.createFolder(folderName, currentParent);
                    currentParent = newFolder.token;
                    createdFolders.push(newFolder);
                }
            }

            console.log(`✅ Folder hierarchy created: ${createdFolders.length} folders`);
            return {
                finalFolder: createdFolders[createdFolders.length - 1],
                createdFolders,
                path: folderPath.join('/'),
                totalFolders: createdFolders.length
            };

        } catch (error) {
            console.error(`❌ Error creating folder hierarchy:`, error.message);
            throw new Error(`Failed to create folder hierarchy: ${error.message}`);
        }
    }

    /**
     * Find folder by name in parent
     * @param {string} folderName - Name to search for
     * @param {string} parentFolderId - Parent folder ID
     * @returns {Promise<object|null>} Folder info or null
     */
    async findFolderByName(folderName, parentFolderId = null) {
        try {
            const children = await this.getFolderChildren(parentFolderId);
            return children.find(item =>
                item.type === 'folder' && item.name === folderName
            ) || null;
        } catch (error) {
            console.warn(`⚠️ Could not search for folder ${folderName}:`, error.message);
            return null;
        }
    }

    /**
     * Get file size from content
     * @param {Buffer|string} fileContent - File content or path
     * @returns {Promise<number>} File size in bytes
     */
    async getFileSize(fileContent) {
        if (Buffer.isBuffer(fileContent)) {
            return fileContent.length;
        } else if (typeof fileContent === 'string') {
            const stats = await fs.promises.stat(fileContent);
            return stats.size;
        }
        throw new Error('Invalid file content type');
    }

    /**
     * Upload small file (< 20MB) using upload_all
     * @param {Buffer|string} fileContent - File content or path
     * @param {string} fileName - File name
     * @param {string} parentFolderId - Parent folder ID
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Upload result
     */
    async uploadSmallFile(fileContent, fileName, parentFolderId = null, progressCallback = null) {
        try {
            const client = await this.auth.getAuthenticatedClient('upload');
            const fileSize = await this.getFileSize(fileContent);

            // Create FormData for multipart upload
            const formData = new FormData();

            if (Buffer.isBuffer(fileContent)) {
                formData.append('file', fileContent, fileName);
            } else if (typeof fileContent === 'string') {
                formData.append('file', fs.createReadStream(fileContent));
            }

            if (!parentFolderId) {
                parentFolderId = await this.getRootFolderMeta();
            }

            formData.append('file_name', fileName);
            formData.append('size', fileSize);
            formData.append('parent_type', 'explorer');
            formData.append('parent_node', parentFolderId);

            console.log(`📤 Uploading small file: ${fileName} (${this.formatFileSize(fileSize)})`);

            if (typeof progressCallback === 'function') {
                progressCallback({ phase: 'uploading', progress: 0, fileName });
            }

            const startTime = Date.now();
            const response = await client.post(this.endpoints.uploadAll, formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Authorization': client.defaults.headers['Authorization']
                },
                maxContentLength: Infinity,
                maxBodyLength: Infinity,
                timeout: 300000, // 5 minutes timeout
                onUploadProgress: (progressEvent) => {
                    if (typeof progressCallback === 'function' && progressEvent.total) {
                        const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
                        progressCallback({
                            phase: 'uploading',
                            progress,
                            fileName,
                            loaded: progressEvent.loaded,
                            total: progressEvent.total
                        });
                    }
                }
            });
            const duration = Date.now() - startTime;

            if (response.data.code === 0) {
                const uploadResult = {
                    ...response.data.data,
                    fileName,
                    fileSize,
                    parentFolderId,
                    uploadTime: duration,
                    uploadedAt: new Date().toISOString()
                };

                this.trackApiCall('upload', true, fileSize);
                console.log(`✅ Small file uploaded: ${uploadResult.file_token} in ${duration}ms`);

                if (typeof progressCallback === 'function') {
                    progressCallback({ phase: 'completed', progress: 100, fileName, result: uploadResult });
                }

                return uploadResult;
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            this.trackApiCall('upload', false);
            console.error(`❌ Error uploading small file ${fileName}:`, error.message);
            throw new Error(`Failed to upload small file ${fileName}: ${error.message}`);
        }
    }

    /**
     * Upload large file using chunked upload
     * @param {Buffer|string} fileContent - File content or path
     * @param {string} fileName - File name
     * @param {string} parentFolderId - Parent folder ID
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Upload result
     */
    async uploadLargeFile(fileContent, fileName, parentFolderId = null, progressCallback = null) {
        try {
            const fileSize = await this.getFileSize(fileContent);
            console.log(`📤 Uploading large file: ${fileName} (${this.formatFileSize(fileSize)})`);

            if (typeof progressCallback === 'function') {
                progressCallback({ phase: 'preparing', progress: 0, fileName });
            }

            // Step 1: Prepare upload
            const prepareResult = await this.prepareUpload(fileName, fileSize, parentFolderId);

            if (typeof progressCallback === 'function') {
                progressCallback({ phase: 'uploading', progress: 0, fileName });
            }

            // Step 2: Upload in chunks
            const uploadResult = await this.uploadInChunks(
                fileContent,
                prepareResult.upload_id,
                fileSize,
                (chunkProgress) => {
                    if (typeof progressCallback === 'function') {
                        progressCallback({
                            phase: 'uploading',
                            progress: chunkProgress.progress,
                            fileName,
                            chunk: chunkProgress.chunk,
                            totalChunks: chunkProgress.totalChunks
                        });
                    }
                }
            );

            if (typeof progressCallback === 'function') {
                progressCallback({ phase: 'finalizing', progress: 95, fileName });
            }

            // Step 3: Finish upload
            const finalResult = await this.finishUpload(prepareResult.upload_id, uploadResult.block_infos.slice(-1)[0].seq);

            const result = {
                ...finalResult,
                fileName,
                fileSize,
                parentFolderId,
                uploadedAt: new Date().toISOString(),
                method: 'chunked'
            };

            this.trackApiCall('upload', true, fileSize);
            console.log(`✅ Large file uploaded: ${result.file_token}`);

            if (typeof progressCallback === 'function') {
                progressCallback({ phase: 'completed', progress: 100, fileName, result });
            }

            return result;

        } catch (error) {
            this.trackApiCall('upload', false);
            console.error(`❌ Error uploading large file ${fileName}:`, error.message);
            throw new Error(`Failed to upload large file ${fileName}: ${error.message}`);
        }
    }

    /**
     * Smart upload - automatically choose method based on file size
     * @param {Buffer|string} fileContent - File content or path
     * @param {string} fileName - File name
     * @param {string} parentFolderId - Parent folder ID
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Upload result
     */
    async uploadFile(fileContent, fileName, parentFolderId = null, progressCallback = null) {
        try {
            const fileSize = await this.getFileSize(fileContent);

            // Check file size limit
            if (fileSize > this.uploadConfig.maxFileSize) {
                throw new Error(`File too large: ${this.formatFileSize(fileSize)}. Maximum allowed: ${this.formatFileSize(this.uploadConfig.maxFileSize)}`);
            }

            // Choose upload method based on file size
            const useChunkedUpload = fileSize > 20 * 1024 * 1024; // 20MB threshold

            if (useChunkedUpload) {
                console.log(`📦 Using chunked upload for large file: ${this.formatFileSize(fileSize)}`);
                return await this.uploadLargeFile(fileContent, fileName, parentFolderId, progressCallback);
            } else {
                console.log(`📄 Using direct upload for small file: ${this.formatFileSize(fileSize)}`);
                return await this.uploadSmallFile(fileContent, fileName, parentFolderId, progressCallback);
            }

        } catch (error) {
            console.error(`❌ Error in smart upload for ${fileName}:`, error.message);
            throw error;
        }
    }

    /**
     * Prepare chunked upload
     * @param {string} fileName - File name
     * @param {number} fileSize - File size
     * @param {string} parentFolderId - Parent folder ID
     * @returns {Promise<object>} Prepare result
     */
    async prepareUpload(fileName, fileSize, parentFolderId = null) {
        try {
            const client = await this.auth.getAuthenticatedClient('upload');

            if (!parentFolderId) {
                parentFolderId = await this.getRootFolderMeta();
            }

            const requestBody = {
                file_name: fileName,
                size: fileSize,
                parent_type: 'explorer',
                parent_node: parentFolderId
            };

            console.log(`🔧 Preparing upload for: ${fileName} (${this.formatFileSize(fileSize)})`);

            const response = await client.post(this.endpoints.uploadPrepare, requestBody);

            if (response.data.code === 0) {
                console.log(`✅ Upload prepared: ${response.data.data.upload_id}`);
                return response.data.data;
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            console.error(`❌ Error preparing upload:`, error.message);
            throw new Error(`Failed to prepare upload: ${error.message}`);
        }
    }

    /**
     * Upload file in chunks
     * @param {Buffer|string} fileContent - File content or path
     * @param {string} uploadId - Upload ID from prepare
     * @param {number} fileSize - File size
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Upload result with block infos
     */
    async uploadInChunks(fileContent, uploadId, fileSize, progressCallback = null) {
        try {
            const chunkSize = this.uploadConfig.chunkSize;
            const totalChunks = Math.ceil(fileSize / chunkSize);
            const blockInfos = [];

            console.log(`📦 Uploading ${totalChunks} chunks of ${this.formatFileSize(chunkSize)} each`);

            let buffer;
            if (Buffer.isBuffer(fileContent)) {
                buffer = fileContent;
            } else {
                buffer = await fs.promises.readFile(fileContent);
            }

            for (let i = 0; i < totalChunks; i++) {
                const start = i * chunkSize;
                const end = Math.min(start + chunkSize, fileSize);
                const chunkBuffer = buffer.slice(start, end);
                const chunkNumber = i + 1;

                console.log(`📤 Uploading chunk ${chunkNumber}/${totalChunks} (${this.formatFileSize(chunkBuffer.length)})`);

                const blockInfo = await this.uploadChunk(uploadId, chunkNumber, chunkBuffer);
                blockInfos.push(blockInfo);

                if (typeof progressCallback === 'function') {
                    progressCallback({
                        progress: Math.round((chunkNumber / totalChunks) * 100),
                        chunk: chunkNumber,
                        totalChunks,
                        chunkSize: chunkBuffer.length
                    });
                }

                // Small delay between chunks to avoid overwhelming the server
                if (i < totalChunks - 1) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            console.log(`✅ All ${totalChunks} chunks uploaded successfully`);
            return { block_infos: blockInfos };

        } catch (error) {
            console.error(`❌ Error uploading chunks:`, error.message);
            throw new Error(`Failed to upload chunks: ${error.message}`);
        }
    }

    /**
     * Upload single chunk
     * @param {string} uploadId - Upload ID
     * @param {number} seq - Chunk sequence number
     * @param {Buffer} chunkBuffer - Chunk data
     * @returns {Promise<object>} Block info
     */
    async uploadChunk(uploadId, seq, chunkBuffer) {
        try {
            const client = await this.auth.getAuthenticatedClient('upload');

            const formData = new FormData();
            formData.append('upload_id', uploadId);
            formData.append('seq', seq.toString());
            formData.append('size', chunkBuffer.length.toString());
            formData.append('file', chunkBuffer, `chunk_${seq}`);

            const response = await client.post(this.endpoints.uploadPart, formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Authorization': client.defaults.headers['Authorization']
                },
                timeout: 60000 // 1 minute timeout per chunk
            });

            if (response.data.code === 0) {
                return {
                    seq,
                    size: chunkBuffer.length,
                    etag: response.data.data.etag || `chunk_${seq}`
                };
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            console.error(`❌ Error uploading chunk ${seq}:`, error.message);
            throw new Error(`Failed to upload chunk ${seq}: ${error.message}`);
        }
    }

    /**
     * Finish chunked upload
     * @param {string} uploadId - Upload ID
     * @param {int} blockNum - Number of blocks
     * @returns {Promise<object>} Final upload result
     */
    async finishUpload(uploadId, blockNum) {
        try {
            const client = await this.auth.getAuthenticatedClient('upload');

            const requestBody = {
                upload_id: uploadId,
                block_num: blockNum
            };

            console.log(`🏁 Finishing upload with ${blockInfos.length} blocks`);

            const response = await client.post(this.endpoints.uploadFinish, requestBody);

            if (response.data.code === 0) {
                console.log(`✅ Upload finished: ${response.data.data.file_token}`);
                return response.data.data;
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            console.error(`❌ Error finishing upload:`, error.message);
            throw new Error(`Failed to finish upload: ${error.message}`);
        }
    }

    /**
     * Format file size to human readable string
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted size
     */
    formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get folder children
     * @param {string} folderId - Folder ID (null for root)
     * @returns {Promise<Array>} Children items
     */
    async getFolderChildren(folderId = null) {
        try {
            const client = await this.auth.getAuthenticatedClient('drive');

            const params = {
                folder_token: folderId,
                page_size: 200
            };

            const response = await client.get(this.endpoints.folderChildren, { params });

            if (response.data.code === 0) {
                return response.data.data.files || [];
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            console.error(`❌ Error getting folder children:`, error.message);
            throw new Error(`Failed to get folder children: ${error.message}`);
        }
    }

    /**
     * Enhanced permissions setting với flexible options
     * @param {string} fileToken - Token của file/folder
     * @param {object} permissionConfig - Permission configuration
     * @returns {Promise<object>} Permission result
     */
    async setPermissions(fileToken, permissionConfig = {}) {
        try {
            const client = await this.auth.getAuthenticatedClient('drive');

            // Default permission configuration
            const type = permissionConfig.type || 'file';
            const permissionType = permissionConfig.perm === 'view' ? 'anyone_readable' : 'anyone_editable';
            const defaultConfig = {
                comment_entity: "anyone_can_view",
                external_access: true,
                invite_external: true,
                link_share_entity: permissionType,
                security_entity: "anyone_can_view",
                share_entity: "anyone"
            };

            const config = { ...defaultConfig, ...permissionConfig };

            console.log(`🔐 Setting permissions for file: ${fileToken}`);
            console.log(`   External access: ${config.external_access}`);
            console.log(`   Security entity: ${config.security_entity}`);

            const startTime = Date.now();
            const response = await client.patch(`${this.endpoints.permissions}/${fileToken}/public?type=${type}`, config);
            const duration = Date.now() - startTime;

            if (response.data.code === 0) {
                this.trackApiCall('setPermissions', true);
                console.log(`✅ Permissions set for file: ${fileToken} in ${duration}ms`);
                return {
                    ...response.data.data,
                    fileToken,
                    config,
                    setAt: new Date().toISOString(),
                    queryTime: duration
                };
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            this.trackApiCall('setPermissions', false);
            console.error(`❌ Error setting permissions for ${fileToken}:`, error.message);
            throw new Error(`Failed to set permissions for ${fileToken}: ${error.message}`);
        }
    }

    /**
     * Enhanced permissions setting với flexible options
     * @param {string} fileToken - Token của file/folder
     * @param {object} permissionConfig - Permission configuration
     * @returns {Promise<object>} Permission result
     */
    async addPermissions(fileToken, permissionConfig = {}) {
        try {
            const client = await this.auth.getAuthenticatedClient('drive');

            const startTime = Date.now();
            const type = permissionConfig.type || 'file';
            const response = await client.post(`${this.endpoints.permissions}/${fileToken}/members?type=${type}`, permissionConfig);
            const duration = Date.now() - startTime;

            if (response.data.code === 0) {
                this.trackApiCall('addPermissions', true);
                console.log(`✅ Add permission for file: ${fileToken} in ${duration}ms`);
                return {
                    ...response.data.data.member,
                    fileToken,
                    config,
                    setAt: new Date().toISOString(),
                    queryTime: duration
                };
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            this.trackApiCall('addPermissions', false);
            console.error(`❌ Error adding permission for ${fileToken}:`, error.message);
            throw new Error(`Failed to Add permission for ${fileToken}: ${error.message}`);
        }
    }

    /**
 * Transfer ownership of a file to another member
 * @param {string} fileToken - Token của file/folder
 * @param {object} emailAddress - Email address of the new owner
 * @returns {Promise<object>} Permission result
 */
    async transferOwnership(fileToken, emailAddress) {
        try {
            const client = await this.auth.getAuthenticatedClient('drive');

            const config = {
                type: "file",
                token: fileToken,
                owner: {
                    member_type: "email",
                    member_id: emailAddress
                }
            }
            const startTime = Date.now();
            const response = await client.post(`/drive/permission/member/transfer`, config);
            const duration = Date.now() - startTime;

            if (response.data.code === 0) {
                this.trackApiCall('transferOwnership', true);
                console.log(`✅ Transfer ownership of a file: ${fileToken} in ${duration}ms`);
                return {
                    ...response.data.data,
                    fileToken,
                    config,
                    setAt: new Date().toISOString(),
                    queryTime: duration
                };
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            this.trackApiCall('transferOwnership', false);
            console.error(`❌ Error transfer ownership of a file ${fileToken}:`, error.message);
            throw new Error(`Failed to transfer ownership of a file ${fileToken}: ${error.message}`);
        }
    }

    /**
     * Set permissions for multiple files
     * @param {Array<string>} fileTokens - Array of file tokens
     * @param {object} permissionConfig - Permission configuration
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Batch permission result
     */
    async setMultiplePermissions(fileTokens, permissionConfig = {}, progressCallback = null) {
        const results = [];
        const errors = [];

        try {
            console.log(`🔐 Setting permissions for ${fileTokens.length} files`);

            for (let i = 0; i < fileTokens.length; i++) {
                const fileToken = fileTokens[i];

                try {
                    const result = await this.addPermissions(fileToken, permissionConfig);
                    results.push(result);

                    if (typeof progressCallback === 'function') {
                        progressCallback({
                            current: i + 1,
                            total: fileTokens.length,
                            fileToken,
                            success: true,
                            errors: errors.length
                        });
                    }

                    // Small delay to avoid overwhelming the server
                    if (i < fileTokens.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }

                } catch (error) {
                    errors.push({
                        fileToken,
                        error: error.message
                    });
                    console.error(`❌ Failed to set permissions for ${fileToken}:`, error.message);
                }
            }

            console.log(`✅ Set permissions for ${results.length} files, ${errors.length} errors`);
            return {
                results,
                errors,
                stats: {
                    total: fileTokens.length,
                    success: results.length,
                    failed: errors.length
                }
            };

        } catch (error) {
            throw new Error(`Failed to set multiple permissions: ${error.message}`);
        }
    }

    /**
     * Lấy thông tin file/folder với caching
     * @param {string} fileToken - Token của file/folder
     * @param {boolean} useCache - Use cache or not
     * @returns {Promise<object>} Enhanced file info
     */
    async getFileInfo(fileToken, useCache = true) {
        try {
            const cacheKey = `fileinfo_${fileToken}`;

            // Check cache
            if (useCache && this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (cached.expires > Date.now()) {
                    console.log(`🔄 Using cached file info for ${fileToken}`);
                    return cached.data;
                }
            }

            const client = await this.auth.getAuthenticatedClient('drive');

            const startTime = Date.now();
            const response = await client.get(`${this.endpoints.fileInfo}/${fileToken}`);
            const duration = Date.now() - startTime;

            if (response.data.code === 0) {
                const fileInfo = {
                    ...response.data.data,
                    queryTime: duration,
                    retrievedAt: new Date().toISOString()
                };

                // Cache the result
                if (useCache) {
                    this.cache.set(cacheKey, {
                        data: fileInfo,
                        expires: Date.now() + (10 * 60 * 1000) // 10 minutes
                    });
                }

                console.log(`📄 Retrieved file info for ${fileToken} in ${duration}ms`);
                return fileInfo;
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            console.error(`❌ Error getting file info for ${fileToken}:`, error.message);
            throw new Error(`Failed to get file info for ${fileToken}: ${error.message}`);
        }
    }

    /**
     * Get root folder metadata
     * @param {boolean} useCache - Use cache or not
     * @returns {Promise<string>} Root folder token
     */
    async getRootFolderMeta(useCache = true) {
        try {
            const cacheKey = 'root_folder_meta';

            // Check cache
            if (useCache && this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (cached.expires > Date.now()) {
                    console.log('🔄 Using cached root folder token');
                    return cached.data.token;
                }
            }

            const client = await this.auth.getAuthenticatedClient('drive');

            console.log('📁 Getting root folder metadata...');

            const startTime = Date.now();
            const response = await client.get(this.endpoints.rootFolderMeta);
            const duration = Date.now() - startTime;

            if (response.data.code === 0) {
                const rootFolderToken = response.data.data.token;
                const rootFolderData = {
                    token: rootFolderToken,
                    ...response.data.data,
                    queryTime: duration,
                    retrievedAt: new Date().toISOString()
                };

                console.log(`✅ Root folder token retrieved: ${rootFolderToken} in ${duration}ms`);

                // Cache root folder info
                if (useCache) {
                    this.cache.set(cacheKey, {
                        data: rootFolderData,
                        expires: Date.now() + (60 * 60 * 1000) // 1 hour cache
                    });
                }

                return rootFolderToken;
            } else {
                throw new Error(`Lark API error (${response.data.code}): ${response.data.msg}`);
            }

        } catch (error) {
            console.error('❌ Error getting root folder meta:', error.message);
            throw new Error(`Failed to get root folder meta: ${error.message}`);
        }
    }

    /**
     * Get statistics
     * @returns {object} API usage statistics
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.size,
            uploadConfig: this.uploadConfig
        };
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
        console.log('🧹 Lark Drive API cache cleared');
    }

    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            apiCalls: 0,
            errors: 0,
            filesUploaded: 0,
            foldersCreated: 0,
            bytesUploaded: 0,
            permissionsSet: 0
        };
        console.log('📊 Lark Drive API stats reset');
    }

    /**
     * Comprehensive API testing
     * @returns {Promise<object>} Detailed test results
     */
    async testAPIs() {
        const results = {
            success: false,
            connection: false,
            operations: {
                createFolder: false,
                uploadSmallFile: false,
                uploadLargeFile: false,
                setPermissions: false,
                getFileInfo: false,
                folderHierarchy: false
            },
            performance: {},
            stats: {},
            errors: [],
            testFiles: []
        };

        try {
            console.log('🔍 Testing Lark Drive APIs...');

            // Test 1: Connection
            console.log('1. Testing connection...');
            const connectionStart = Date.now();
            const connectionResult = await this.auth.testConnection();
            results.connection = connectionResult.success;
            results.performance.connection = Date.now() - connectionStart;

            if (!results.connection) {
                results.errors.push('Connection failed');
                return results;
            }

            console.log(`✅ Connection successful in ${results.performance.connection}ms`);

            // Test 2: Create folder
            console.log('2. Testing create folder...');
            const folderStart = Date.now();
            const testFolderName = `test-migration-${Date.now()}`;
            const folderResult = await this.createFolder(testFolderName);
            results.operations.createFolder = !!folderResult.token;
            results.performance.createFolder = Date.now() - folderStart;

            console.log(`✅ Folder created: ${folderResult.token} in ${results.performance.createFolder}ms`);

            if (results.operations.createFolder) {
                // Test 3: Upload small file
                console.log('3. Testing small file upload...');
                const smallUploadStart = Date.now();
                const smallTestContent = Buffer.from('This is a small test file for Lark Drive migration testing.', 'utf8');
                const smallUploadResult = await this.uploadFile(
                    smallTestContent,
                    'small-test-file.txt',
                    folderResult.token,
                    (progress) => {
                        console.log(`   📊 Small file progress: ${progress.progress}% (${progress.phase})`);
                    }
                );
                results.operations.uploadSmallFile = !!smallUploadResult.file_token;
                results.performance.uploadSmallFile = Date.now() - smallUploadStart;
                results.testFiles.push(smallUploadResult);

                console.log(`✅ Small file uploaded: ${smallUploadResult.file_token} in ${results.performance.uploadSmallFile}ms`);

                // Test 4: Upload larger file (simulate)
                console.log('4. Testing larger file upload...');
                const largeUploadStart = Date.now();
                const largeTestContent = Buffer.alloc(5 * 1024 * 1024, 'A'); // 5MB file
                const largeUploadResult = await this.uploadFile(
                    largeTestContent,
                    'large-test-file.bin',
                    folderResult.token,
                    (progress) => {
                        console.log(`   📊 Large file progress: ${progress.progress}% (${progress.phase})`);
                    }
                );
                results.operations.uploadLargeFile = !!largeUploadResult.file_token;
                results.performance.uploadLargeFile = Date.now() - largeUploadStart;
                results.testFiles.push(largeUploadResult);

                console.log(`✅ Large file uploaded: ${largeUploadResult.file_token} in ${results.performance.uploadLargeFile}ms`);

                // Test 5: Set permissions
                console.log('5. Testing set permissions...');
                const permissionsStart = Date.now();
                const permissionResult = await this.addPermissions(smallUploadResult.file_token, {
                    external_access: false,
                    security_entity: 'tenant_editable'
                });
                results.operations.setPermissions = !!permissionResult;
                results.performance.setPermissions = Date.now() - permissionsStart;

                console.log(`✅ Permissions set in ${results.performance.setPermissions}ms`);

                // Test 6: Get file info
                console.log('6. Testing get file info...');
                const fileInfoStart = Date.now();
                const fileInfo = await this.getFileInfo(smallUploadResult.file_token);
                results.operations.getFileInfo = !!fileInfo.token;
                results.performance.getFileInfo = Date.now() - fileInfoStart;

                console.log(`✅ File info retrieved in ${results.performance.getFileInfo}ms`);

                // Test 7: Folder hierarchy
                console.log('7. Testing folder hierarchy creation...');
                const hierarchyStart = Date.now();
                const hierarchyResult = await this.createFolderHierarchy(
                    ['Level1', 'Level2', 'Level3'],
                    folderResult.token
                );
                results.operations.folderHierarchy = !!hierarchyResult.finalFolder;
                results.performance.folderHierarchy = Date.now() - hierarchyStart;

                console.log(`✅ Folder hierarchy created in ${results.performance.folderHierarchy}ms`);
            }

            // Get final stats
            results.stats = this.getStats();
            results.success = Object.values(results.operations).every(op => op === true);

            console.log(`🎉 API testing completed! Success: ${results.success}`);

        } catch (error) {
            results.errors.push(error.message);
            console.error('❌ API test error:', error.message);
        }

        return results;
    }

    /**
     * Performance benchmark
     * @returns {Promise<object>} Benchmark results
     */
    async benchmark() {
        console.log('🏃 Running Lark Drive API performance benchmark...');

        const results = {
            operations: {},
            uploadPerformance: {},
            stats: {}
        };

        try {
            // Clear cache and reset stats for fair test
            this.clearCache();
            this.resetStats();

            // Benchmark 1: Folder creation
            console.log('1. Benchmarking folder creation...');
            const folderTimes = [];
            for (let i = 0; i < 3; i++) {
                const start = Date.now();
                await this.createFolder(`benchmark-folder-${i}-${Date.now()}`);
                folderTimes.push(Date.now() - start);
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            results.operations.createFolder = {
                times: folderTimes,
                average: folderTimes.reduce((a, b) => a + b, 0) / folderTimes.length,
                min: Math.min(...folderTimes),
                max: Math.max(...folderTimes)
            };

            // Benchmark 2: Upload performance by size
            console.log('2. Benchmarking upload performance...');
            const uploadSizes = [
                { size: 1024, name: '1KB' },
                { size: 100 * 1024, name: '100KB' },
                { size: 1024 * 1024, name: '1MB' }
            ];

            for (const testSize of uploadSizes) {
                const testContent = Buffer.alloc(testSize.size, 'X');
                const start = Date.now();
                await this.uploadFile(testContent, `benchmark-${testSize.name}.bin`);
                const duration = Date.now() - start;

                results.uploadPerformance[testSize.name] = {
                    size: testSize.size,
                    duration,
                    throughput: (testSize.size / duration * 1000).toFixed(2) + ' bytes/sec'
                };
            }

            results.stats = this.getStats();
            console.log('✅ Benchmark completed!');

        } catch (error) {
            console.error('❌ Benchmark failed:', error.message);
            results.error = error.message;
        }

        return results;
    }
}

// Export singleton instance
export const larkDriveAPI = new LarkDriveAPI();