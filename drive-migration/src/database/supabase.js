import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Supabase Database Client
 * Quản lý kết nối và operations với Supabase PostgreSQL
 */
export class SupabaseClient {
    constructor() {
        this.supabaseUrl = process.env.SUPABASE_URL;
        this.supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
        this.supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

        if (!this.supabaseUrl || !this.supabaseAnonKey || !this.supabaseServiceKey) {
            throw new Error('Missing required Supabase credentials (SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY)');
        }

        // Client cho user operations (với RLS)
        this.client = createClient(this.supabaseUrl, this.supabaseAnonKey, {
            auth: {
                autoRefreshToken: true,
                persistSession: true
            },
            realtime: {
                params: {
                    eventsPerSecond: 10
                }
            }
        });

        // Service client cho admin operations (bypass RLS)
        this.serviceClient = createClient(this.supabaseUrl, this.supabaseServiceKey, {
            auth: {
                autoRefreshToken: false,
                persistSession: false
            }
        });
    }

    /**
     * Lấy client thường (với RLS)
     */
    getClient() {
        return this.client;
    }

    /**
     * Lấy service client (bypass RLS)
     */
    getServiceClient() {
        return this.serviceClient;
    }

    /**
     * Test kết nối database
     */
    async testConnection() {
        try {
            const { data, error } = await this.serviceClient
                .from('migration_tasks')
                .select('*')
                .limit(1);

            if (error) {
                throw error;
            }

            return {
                success: true,
                message: 'Supabase connection successful',
                data
            };
        } catch (error) {
            return {
                success: false,
                message: 'Supabase connection failed',
                error: error.message
            };
        }
    }

    /**
     * Tạo migration task mới
     */
    async createMigrationTask(taskData) {
        try {
            const { data, error } = await this.serviceClient
                .from('migration_tasks')
                .insert([{
                    owner_email: taskData.owner_email,
                    scope: taskData.scope,
                    path: taskData.path || null,
                    config: taskData.config || {}
                }])
                .select()
                .single();

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Cập nhật trạng thái migration task
     */
    async updateMigrationTask(taskId, updates) {
        try {
            const { data, error } = await this.serviceClient
                .from('migration_tasks')
                .update(updates)
                .eq('id', taskId)
                .select()
                .single();

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Lấy thông tin migration task
     */
    async getMigrationTask(taskId) {
        try {
            const { data, error } = await this.serviceClient
                .from('migration_tasks')
                .select('*')
                .eq('id', taskId)
                .single();

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Tạo migration item
     */
    async createMigrationItem(itemData) {
        try {
            const { data, error } = await this.serviceClient
                .from('migration_items')
                .insert([itemData])
                .select()
                .single();

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Cập nhật migration item
     */
    async updateMigrationItem(itemId, updates) {
        try {
            const { data, error } = await this.serviceClient
                .from('migration_items')
                .update(updates)
                .eq('id', itemId)
                .select()
                .single();

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Lấy danh sách migration items của task
     */
    async getMigrationItems(taskId, status = null) {
        try {
            let query = this.serviceClient
                .from('migration_items')
                .select('*')
                .eq('task_id', taskId);

            if (status) {
                query = query.eq('status', status);
            }

            const { data, error } = await query.order('created_at', { ascending: true });

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Tạo user mapping
     */
    async createUserMapping(email_google, lark_userid = null) {
        try {
            const { data, error } = await this.serviceClient
                .from('users')
                .upsert([{
                    email_google,
                    lark_userid,
                    mapped: !!lark_userid
                }])
                .select()
                .single();

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Lấy user mapping
     */
    async getUserMapping(email_google) {
        try {
            const { data, error } = await this.serviceClient
                .from('users')
                .select('*')
                .eq('email_google', email_google)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = not found
                throw error;
            }

            return { success: true, data: data || null };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Lấy danh sách user mappings chưa được map
     */
    async getUnmappedUsers() {
        try {
            const { data, error } = await this.serviceClient
                .from('users')
                .select('*')
                .eq('mapped', false)
                .order('created_at', { ascending: true });

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Tạo log entry
     */
    async createLog(logData) {
        try {
            const { data, error } = await this.serviceClient
                .from('migration_logs')
                .insert([logData])
                .select()
                .single();

            if (error) throw error;

            return { success: true, data };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Subscribe to realtime changes
     */
    subscribeToTask(taskId, callback) {
        return this.client
            .channel(`migration_task_${taskId}`)
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'migration_tasks',
                filter: `id=eq.${taskId}`
            }, callback)
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'migration_items',
                filter: `task_id=eq.${taskId}`
            }, callback)
            .subscribe();
    }

    /**
     * Unsubscribe from realtime
     */
    unsubscribe(subscription) {
        return this.client.removeChannel(subscription);
    }
}

// Export singleton instance
export const supabaseClient = new SupabaseClient();
