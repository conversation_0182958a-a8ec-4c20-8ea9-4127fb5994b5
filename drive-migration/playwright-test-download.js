import { chromium } from 'playwright';

async function testDownloadSessionCreation() {
    console.log('🎭 Starting Playwright test for Download Session Creation...');

    const browser = await chromium.launch({
        headless: false, // Set to true for headless mode
        slowMo: 1000 // Slow down actions for better visibility
    });

    const context = await browser.newContext();
    const page = await context.newPage();

    // Listen for console messages
    page.on('console', msg => {
        console.log(`🖥️ Console ${msg.type()}: ${msg.text()}`);
    });

    // Listen for network requests
    page.on('response', response => {
        if (response.url().includes('/api/')) {
            console.log(`🌐 API Response: ${response.status()} ${response.url()}`);
        }
    });

    try {
        // Navigate to the download page
        console.log('📍 Navigating to download page...');
        await page.goto('http://localhost:5174/download');

        // Wait for the page to load
        await page.waitForLoadState('networkidle');

        // Take a screenshot of the initial state
        await page.screenshot({ path: 'download-page-initial.png' });
        console.log('📸 Screenshot saved: download-page-initial.png');

        // Check if the page loaded correctly
        const pageTitle = await page.title();
        console.log(`📄 Page title: ${pageTitle}`);

        // Look for the "Create New Session" button or similar
        console.log('🔍 Looking for Create New Session button...');

        // Wait for the main content to load
        await page.waitForSelector('.download-page', { timeout: 10000 });

        // Check if there are existing sessions or if we need to create a new one
        const hasExistingSessions = await page.locator('.session-card').count() > 0;
        console.log(`📊 Existing sessions found: ${hasExistingSessions}`);

        // Look for the "Create New Session" button
        const createButton = page.locator('button:has-text("Create New Download Session"), button:has-text("Create New Session"), button:has-text("New Session")');
        const createButtonExists = await createButton.count() > 0;

        if (createButtonExists) {
            console.log('✅ Create New Session button found');
            await createButton.click();
            console.log('🖱️ Clicked Create New Session button');

            // Wait for the form to appear
            await page.waitForSelector('.download-config-form, form, .config-form', { timeout: 5000 });
            console.log('📝 Download configuration form appeared');

            // Take a screenshot of the form
            await page.screenshot({ path: 'download-form.png' });
            console.log('📸 Screenshot saved: download-form.png');

            // Fill in the form
            console.log('📝 Filling in the form...');

            // Session name
            const sessionNameInput = page.locator('input[name="name"], input[placeholder*="name"], input[placeholder*="Session"]').first();
            if (await sessionNameInput.count() > 0) {
                await sessionNameInput.fill(`Test Session ${new Date().toISOString()}`);
                console.log('✅ Session name filled');
            }

            // Select users - look for checkboxes or user selection
            const userCheckboxes = page.locator('input[type="checkbox"]');
            const userCheckboxCount = await userCheckboxes.count();
            console.log(`👥 Found ${userCheckboxCount} user checkboxes`);

            if (userCheckboxCount > 0) {
                // Select the first few users
                for (let i = 0; i < Math.min(3, userCheckboxCount); i++) {
                    await userCheckboxes.nth(i).check();
                }
                console.log('✅ Users selected');
            }

            // Download path
            const downloadPathInput = page.locator('input[name="downloadPath"], input[placeholder*="path"], input[placeholder*="Path"]').first();
            if (await downloadPathInput.count() > 0) {
                await downloadPathInput.fill('/tmp/test-downloads');
                console.log('✅ Download path filled');
            }

            // Concurrent downloads (if exists)
            const concurrentInput = page.locator('input[name="concurrentDownloads"], input[type="number"]').first();
            if (await concurrentInput.count() > 0) {
                await concurrentInput.fill('2');
                console.log('✅ Concurrent downloads set');
            }

            // Take a screenshot of the filled form
            await page.screenshot({ path: 'download-form-filled.png' });
            console.log('📸 Screenshot saved: download-form-filled.png');

            // Submit the form
            const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Submit")');
            const submitButtonExists = await submitButton.count() > 0;

            if (submitButtonExists) {
                console.log('🚀 Submitting the form...');
                await submitButton.click();

                // Wait for response - either success message or error
                await page.waitForTimeout(5000);

                // Check for success/error messages
                const successMessage = page.locator('.toast-success, .alert-success, .success-message, .notification-success');
                const errorMessage = page.locator('.toast-error, .alert-error, .error-message, .notification-error');

                const hasSuccess = await successMessage.count() > 0;
                const hasError = await errorMessage.count() > 0;

                if (hasSuccess) {
                    const successText = await successMessage.first().textContent();
                    console.log(`✅ Success: ${successText}`);
                } else if (hasError) {
                    const errorText = await errorMessage.first().textContent();
                    console.log(`❌ Error: ${errorText}`);
                } else {
                    console.log('⚠️ No clear success/error message found');

                    // Check if we're now on a different step or if the session was created
                    const currentUrl = page.url();
                    console.log(`📍 Current URL after submission: ${currentUrl}`);

                    // Check if there are new sessions in the list
                    const sessionCount = await page.locator('.session-card').count();
                    console.log(`📊 Total sessions after submission: ${sessionCount}`);

                    // Look for any text that might indicate success
                    const pageText = await page.locator('body').textContent();
                    if (pageText.includes('Test Session 2025')) {
                        console.log('✅ Found test session in page content - likely successful!');
                    }
                }

                // Take a screenshot of the result
                await page.screenshot({ path: 'download-form-result.png' });
                console.log('📸 Screenshot saved: download-form-result.png');

                // Check if we're redirected to progress page or session list
                await page.waitForTimeout(2000);
                const currentUrl = page.url();
                console.log(`📍 Current URL: ${currentUrl}`);

            } else {
                console.log('❌ Submit button not found');
            }

        } else {
            console.log('❌ Create New Session button not found');

            // Let's see what's actually on the page
            const pageContent = await page.locator('body').textContent();
            console.log('📄 Page content preview:', pageContent.substring(0, 500));

            // Look for any buttons
            const allButtons = page.locator('button');
            const buttonCount = await allButtons.count();
            console.log(`🔘 Found ${buttonCount} buttons on the page`);

            for (let i = 0; i < Math.min(5, buttonCount); i++) {
                const buttonText = await allButtons.nth(i).textContent();
                console.log(`  Button ${i + 1}: "${buttonText}"`);
            }
        }

        // Final screenshot
        await page.screenshot({ path: 'download-test-final.png' });
        console.log('📸 Final screenshot saved: download-test-final.png');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        await page.screenshot({ path: 'download-test-error.png' });
        console.log('📸 Error screenshot saved: download-test-error.png');
    } finally {
        await browser.close();
        console.log('🎭 Browser closed');
    }
}

// Run the test
testDownloadSessionCreation().catch(console.error);
