# Production Environment Configuration
# Copy this file to .env.production for production deployment

# Environment
NODE_ENV=production
PORT=3000
DOMAIN=migration.company.com

# Production Database (Supabase Production Project)
SUPABASE_URL=https://your-prod-project.supabase.co
SUPABASE_ANON_KEY=your-prod-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-prod-service-role-key

# Google Service Account (Production)
# Google credentials are loaded from google-service-account.json file
# No need for environment variables for Google authentication

# Google Admin & Users (Production) - optional, can be passed as command line arguments
# GOOGLE_ADMIN_EMAIL=<EMAIL>
# GOOGLE_DOMAIN=your-company.com

# Lark App (Production)
LARK_APP_ID=cli_your_production_app_id
LARK_APP_SECRET=your_production_app_secret

# Security Settings
SESSION_SECRET=your-super-secure-session-secret-min-32-chars
JWT_SECRET=your-jwt-secret-key-min-32-chars
ENCRYPTION_KEY=your-encryption-key-32-chars
CORS_ORIGIN=https://migration.company.com

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/ssl/certs/migration.company.com.crt
SSL_KEY_PATH=/etc/ssl/private/migration.company.com.key
FORCE_HTTPS=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# Performance Settings
MAX_CONCURRENT_MIGRATIONS=10
MAX_FILE_SIZE=**********
CHUNK_SIZE=********
CONNECTION_POOL_SIZE=20
QUERY_TIMEOUT=30000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/production.log
ERROR_LOG_FILE=logs/production-errors.log
ACCESS_LOG_FILE=logs/access.log
LOG_ROTATION=daily
LOG_MAX_FILES=30

# Monitoring & Alerts
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
METRICS_COLLECTION=true
ALERT_EMAIL=<EMAIL>
WEBHOOK_URL=https://monitoring.company.com/webhook

# Backup & Recovery
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups
DATABASE_BACKUP_ENABLED=true

# External Services
REDIS_URL=redis://redis.company.com:6379
ELASTICSEARCH_URL=https://elasticsearch.company.com:9200
SENTRY_DSN=https://<EMAIL>/project-id

# Email Configuration
SMTP_HOST=smtp.company.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password
EMAIL_FROM=Drive Migration <<EMAIL>>

# File Storage
UPLOAD_PATH=/var/uploads
TEMP_PATH=/var/temp
MAX_UPLOAD_SIZE=**********
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif,mp4,avi,zip,rar

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
MEMORY_CACHE_SIZE=100

# Database Connection Pool
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000

# API Configuration
API_TIMEOUT=30000
API_RETRY_ATTEMPTS=3
API_RETRY_DELAY=1000
API_MAX_CONCURRENT_REQUESTS=50

# Google API Settings
GOOGLE_API_TIMEOUT=30000
GOOGLE_API_RETRY_ATTEMPTS=3
GOOGLE_DRIVE_CHUNK_SIZE=********
GOOGLE_RATE_LIMIT_REQUESTS_PER_SECOND=10

# Lark API Settings
LARK_API_TIMEOUT=30000
LARK_API_RETRY_ATTEMPTS=3
LARK_UPLOAD_CHUNK_SIZE=********
LARK_RATE_LIMIT_REQUESTS_PER_SECOND=10

# Migration Settings
MIGRATION_BATCH_SIZE=100
MIGRATION_CONCURRENT_FILES=5
MIGRATION_RETRY_ATTEMPTS=3
MIGRATION_TIMEOUT=300000
PRESERVE_TIMESTAMPS=true
VERIFY_CHECKSUMS=true

# Real-time Settings
REALTIME_ENABLED=true
REALTIME_HEARTBEAT_INTERVAL=30000
REALTIME_MAX_CONNECTIONS=1000
REALTIME_MESSAGE_BUFFER_SIZE=100

# Security Headers
HELMET_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
REFERRER_POLICY=strict-origin-when-cross-origin
X_FRAME_OPTIONS=DENY

# Compression
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6
COMPRESSION_THRESHOLD=1024

# Static Files
STATIC_FILES_CACHE_CONTROL=public, max-age=31536000
STATIC_FILES_ETAG=true
STATIC_FILES_GZIP=true

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_TIMEOUT=5000
READINESS_CHECK_PATH=/ready
LIVENESS_CHECK_PATH=/live

# Graceful Shutdown
GRACEFUL_SHUTDOWN_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000
HEADERS_TIMEOUT=60000

# Process Management
PM2_INSTANCES=max
PM2_MAX_MEMORY_RESTART=1000M
PM2_AUTO_RESTART=true
PM2_WATCH=false

# Environment Validation
VALIDATE_ENV_ON_START=true
REQUIRE_ALL_ENV_VARS=true
ENV_VALIDATION_STRICT=true
