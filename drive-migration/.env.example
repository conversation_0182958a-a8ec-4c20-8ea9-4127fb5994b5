# Google Service Account Configuration
# Google credentials are loaded from google-service-account.json file
# You can override the path using GOOGLE_SERVICE_ACCOUNT_PATH

# Google Domain Configuration (osp.vn or osp.com.vn)
GOOGLE_DOMAIN=your-domain.com

# Google Service Account JSON File Path (optional - defaults to google-service-account.json)
GOOGLE_SERVICE_ACCOUNT_PATH=./google-service-account.json

# Google Admin Email (optional - defaults to admin@{GOOGLE_DOMAIN})
GOOGLE_ADMIN_EMAIL=<EMAIL>

# Google Testing Configuration (optional - can be passed as command line arguments)
# GOOGLE_TEST_EMAIL=<EMAIL>
# GOOGLE_TEST_USERS=<EMAIL>,<EMAIL>

# Lark Configuration
LARK_APP_ID=your-lark-app-id
LARK_APP_SECRET=your-lark-app-secret
LARK_TENANT_ACCESS_TOKEN=your-tenant-access-token

# Supabase Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info