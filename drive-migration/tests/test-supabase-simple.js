import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 Testing Supabase Database Connection...\n');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Service Key:', supabaseServiceKey ? 'Set' : 'Missing');

if (!supabaseUrl || !supabaseServiceKey) {
    console.log('❌ Missing Supabase credentials');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test 1: List tables
console.log('\n1. Testing table listing...');
try {
    const { data, error } = await supabase
        .from('migration_tasks')
        .select('*')
        .limit(1);

    if (error) {
        console.log('❌ Error:', error.message);
    } else {
        console.log('✅ migration_tasks table accessible');
    }
} catch (err) {
    console.log('❌ Exception:', err.message);
}

// Test 2: Test all tables
console.log('\n2. Testing all tables...');
const tables = ['users', 'migration_tasks', 'migration_items', 'permission_mappings', 'migration_logs'];

for (const table of tables) {
    try {
        const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1);

        if (error) {
            console.log(`❌ ${table}: ${error.message}`);
        } else {
            console.log(`✅ ${table}: OK`);
        }
    } catch (err) {
        console.log(`❌ ${table}: ${err.message}`);
    }
}

// Test 3: Test view
console.log('\n3. Testing view...');
try {
    const { data, error } = await supabase
        .from('migration_task_stats')
        .select('*')
        .limit(1);

    if (error) {
        console.log(`❌ migration_task_stats view: ${error.message}`);
    } else {
        console.log('✅ migration_task_stats view: OK');
    }
} catch (err) {
    console.log(`❌ migration_task_stats view: ${err.message}`);
}

// Test 4: Insert test data
console.log('\n4. Testing data insertion...');
try {
    const { data, error } = await supabase
        .from('users')
        .insert({
            email_google: '<EMAIL>',
            lark_userid: 'ou_test123',
            mapped: true
        })
        .select();

    if (error) {
        console.log(`❌ Insert test: ${error.message}`);
    } else {
        console.log('✅ Insert test: OK');
        console.log('Data:', data);

        // Clean up
        await supabase
            .from('users')
            .delete()
            .eq('email_google', '<EMAIL>');
        console.log('✅ Cleanup: OK');
    }
} catch (err) {
    console.log(`❌ Insert test: ${err.message}`);
}

console.log('\n🎉 Database testing completed!');
