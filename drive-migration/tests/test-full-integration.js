import { fileDownloadEngine } from './services/file-download-engine.js';
import { larkUploadEngine } from './services/lark-upload-engine.js';
import { userMappingService } from './services/user-mapping-service.js';
import { migrationEngine } from './services/migration-engine.js';
import { realtimeService } from './services/realtime-service.js';
import { supabaseClient } from './database/supabase.js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

dotenv.config();

/**
 * Full Integration Test Suite
 * Comprehensive testing cho toàn bộ migration system
 */

class IntegrationTestSuite {
    constructor() {
        this.testResults = {
            passed: 0,
            failed: 0,
            errors: []
        };
        this.testData = {
            userEmail: '<EMAIL>',
            sessionId: null,
            migrationId: null,
            testFiles: []
        };
    }

    /**
     * Run all integration tests
     */
    async runAllTests() {
        console.log('🚀 Starting Full Integration Test Suite\n');
        console.log('=' .repeat(60));

        try {
            // Test 1: Database Connection
            await this.testDatabaseConnection();
            
            // Test 2: Services Initialization
            await this.testServicesInitialization();
            
            // Test 3: User Mapping System
            await this.testUserMappingSystem();
            
            // Test 4: File Download Engine
            await this.testFileDownloadEngine();
            
            // Test 5: Lark Upload Engine
            await this.testLarkUploadEngine();
            
            // Test 6: Realtime Service
            await this.testRealtimeService();
            
            // Test 7: Migration Engine
            await this.testMigrationEngine();
            
            // Test 8: End-to-End Migration
            await this.testEndToEndMigration();
            
            // Test 9: Error Scenarios
            await this.testErrorScenarios();
            
            // Test 10: Performance Testing
            await this.testPerformance();

            this.printTestSummary();

        } catch (error) {
            console.error('❌ Test suite execution failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(error.message);
        }
    }

    /**
     * Test database connection
     */
    async testDatabaseConnection() {
        console.log('\n1️⃣ Testing Database Connection...');
        
        try {
            // Test Supabase connection
            const { data, error } = await supabaseClient
                .from('users')
                .select('count')
                .limit(1);

            if (error) {
                throw new Error(`Database connection failed: ${error.message}`);
            }

            console.log('✅ Database connection successful');
            this.testResults.passed++;

        } catch (error) {
            console.error('❌ Database connection failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`Database: ${error.message}`);
        }
    }

    /**
     * Test services initialization
     */
    async testServicesInitialization() {
        console.log('\n2️⃣ Testing Services Initialization...');
        
        try {
            // Test all services are properly initialized
            const services = [
                { name: 'File Download Engine', service: fileDownloadEngine },
                { name: 'Lark Upload Engine', service: larkUploadEngine },
                { name: 'User Mapping Service', service: userMappingService },
                { name: 'Migration Engine', service: migrationEngine },
                { name: 'Realtime Service', service: realtimeService }
            ];

            for (const { name, service } of services) {
                if (!service) {
                    throw new Error(`${name} not initialized`);
                }
                
                // Test service has required methods
                if (typeof service.getStats !== 'function') {
                    throw new Error(`${name} missing getStats method`);
                }
                
                console.log(`✅ ${name} initialized`);
            }

            this.testResults.passed++;

        } catch (error) {
            console.error('❌ Services initialization failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`Services: ${error.message}`);
        }
    }

    /**
     * Test user mapping system
     */
    async testUserMappingSystem() {
        console.log('\n3️⃣ Testing User Mapping System...');
        
        try {
            // Test user mapping initialization
            const mockPermissions = [
                {
                    type: 'user',
                    emailAddress: '<EMAIL>',
                    displayName: 'Test User 1',
                    role: 'owner'
                },
                {
                    type: 'user',
                    emailAddress: '<EMAIL>',
                    displayName: 'Test User 2',
                    role: 'editor'
                }
            ];

            const result = await userMappingService.initializeUserMapping(mockPermissions);
            
            if (!result.success) {
                throw new Error('User mapping initialization failed');
            }

            console.log(`✅ User mapping initialized: ${result.newUsers} new users`);
            
            // Test auto-mapping
            const autoMappingResult = await userMappingService.performAutoMapping([
                { email: '<EMAIL>', displayName: 'Test User 1' }
            ]);
            
            console.log(`✅ Auto-mapping completed: ${autoMappingResult.autoMappedCount} mapped`);
            
            this.testResults.passed++;

        } catch (error) {
            console.error('❌ User mapping test failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`User Mapping: ${error.message}`);
        }
    }

    /**
     * Test file download engine
     */
    async testFileDownloadEngine() {
        console.log('\n4️⃣ Testing File Download Engine...');
        
        try {
            // Create test file
            const testFilePath = './temp/test-file.txt';
            const testContent = 'This is a test file for download engine testing.';
            
            // Ensure temp directory exists
            if (!fs.existsSync('./temp')) {
                fs.mkdirSync('./temp', { recursive: true });
            }
            
            fs.writeFileSync(testFilePath, testContent);

            // Mock file info
            const mockFileInfo = {
                id: 'test_file_123',
                name: 'test-file.txt',
                mimeType: 'text/plain',
                size: testContent.length
            };

            // Test download engine stats
            const stats = fileDownloadEngine.getStats();
            console.log('✅ Download engine stats retrieved:', stats.totalDownloads);

            // Cleanup
            if (fs.existsSync(testFilePath)) {
                fs.unlinkSync(testFilePath);
            }

            this.testResults.passed++;

        } catch (error) {
            console.error('❌ File download engine test failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`Download Engine: ${error.message}`);
        }
    }

    /**
     * Test Lark upload engine
     */
    async testLarkUploadEngine() {
        console.log('\n5️⃣ Testing Lark Upload Engine...');
        
        try {
            // Test upload engine configuration
            const stats = larkUploadEngine.getStats();
            console.log('✅ Upload engine stats retrieved:', stats.totalUploads);

            // Test MIME type detection
            const mimeType = larkUploadEngine.getMimeType('test.pdf');
            if (mimeType !== 'application/pdf') {
                throw new Error('MIME type detection failed');
            }
            console.log('✅ MIME type detection working');

            // Test file size formatting
            const formattedSize = larkUploadEngine.formatFileSize(1024 * 1024);
            if (!formattedSize.includes('MB')) {
                throw new Error('File size formatting failed');
            }
            console.log('✅ File size formatting working');

            this.testResults.passed++;

        } catch (error) {
            console.error('❌ Lark upload engine test failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`Upload Engine: ${error.message}`);
        }
    }

    /**
     * Test realtime service
     */
    async testRealtimeService() {
        console.log('\n6️⃣ Testing Realtime Service...');
        
        try {
            // Test health check
            const healthCheck = await realtimeService.healthCheck();
            if (!healthCheck.healthy) {
                throw new Error(`Realtime health check failed: ${healthCheck.error}`);
            }
            console.log('✅ Realtime service health check passed');

            // Test channel creation
            const testMigrationId = 'test_integration_' + Date.now();
            const channelInfo = realtimeService.createMigrationChannel(testMigrationId);
            if (!channelInfo) {
                throw new Error('Channel creation failed');
            }
            console.log('✅ Realtime channel created');

            // Test message broadcasting
            await realtimeService.broadcastStatusChange(testMigrationId, 'running');
            console.log('✅ Message broadcasting working');

            // Cleanup
            await realtimeService.cleanupChannel(testMigrationId);
            console.log('✅ Channel cleanup completed');

            this.testResults.passed++;

        } catch (error) {
            console.error('❌ Realtime service test failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`Realtime: ${error.message}`);
        }
    }

    /**
     * Test migration engine
     */
    async testMigrationEngine() {
        console.log('\n7️⃣ Testing Migration Engine...');
        
        try {
            // Test migration engine stats
            const stats = migrationEngine.getStats();
            console.log('✅ Migration engine stats retrieved');

            // Test active migrations tracking
            const activeMigrations = migrationEngine.getActiveMigrations();
            console.log(`✅ Active migrations: ${activeMigrations.length}`);

            this.testResults.passed++;

        } catch (error) {
            console.error('❌ Migration engine test failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`Migration Engine: ${error.message}`);
        }
    }

    /**
     * Test end-to-end migration (mock)
     */
    async testEndToEndMigration() {
        console.log('\n8️⃣ Testing End-to-End Migration (Mock)...');
        
        try {
            // Create mock scan session
            const sessionId = 'test_session_' + Date.now();
            
            const { data: session, error: sessionError } = await supabaseClient
                .from('scan_sessions')
                .insert({
                    id: sessionId,
                    user_email: this.testData.userEmail,
                    scan_type: 'full_drive',
                    status: 'completed',
                    total_files: 3,
                    scanned_files: 3
                })
                .select()
                .single();

            if (sessionError) {
                throw new Error(`Failed to create test session: ${sessionError.message}`);
            }

            // Create mock scanned files
            const mockFiles = [
                {
                    scan_session_id: sessionId,
                    file_id: 'test_file_1',
                    name: 'document1.pdf',
                    mime_type: 'application/pdf',
                    size: 1024,
                    full_path: '/Documents/document1.pdf',
                    is_selected: true
                },
                {
                    scan_session_id: sessionId,
                    file_id: 'test_file_2',
                    name: 'spreadsheet1.xlsx',
                    mime_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    size: 2048,
                    full_path: '/Documents/spreadsheet1.xlsx',
                    is_selected: true
                }
            ];

            const { error: filesError } = await supabaseClient
                .from('scanned_files')
                .insert(mockFiles);

            if (filesError) {
                throw new Error(`Failed to create test files: ${filesError.message}`);
            }

            console.log('✅ Mock migration data created');
            console.log(`✅ Session ID: ${sessionId}`);
            
            // Store for cleanup
            this.testData.sessionId = sessionId;

            this.testResults.passed++;

        } catch (error) {
            console.error('❌ End-to-end migration test failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`E2E Migration: ${error.message}`);
        }
    }

    /**
     * Test error scenarios
     */
    async testErrorScenarios() {
        console.log('\n9️⃣ Testing Error Scenarios...');
        
        try {
            // Test invalid migration ID
            try {
                await realtimeService.broadcastMigrationProgress('invalid_id', {});
                console.log('✅ Invalid migration ID handled gracefully');
            } catch (error) {
                // Expected to handle gracefully
                console.log('✅ Error handling working for invalid migration ID');
            }

            // Test database error handling
            try {
                await supabaseClient
                    .from('non_existent_table')
                    .select('*');
            } catch (error) {
                console.log('✅ Database error handling working');
            }

            this.testResults.passed++;

        } catch (error) {
            console.error('❌ Error scenarios test failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`Error Scenarios: ${error.message}`);
        }
    }

    /**
     * Test performance
     */
    async testPerformance() {
        console.log('\n🔟 Testing Performance...');
        
        try {
            // Test service response times
            const startTime = Date.now();
            
            // Test multiple service calls
            await Promise.all([
                fileDownloadEngine.getStats(),
                larkUploadEngine.getStats(),
                userMappingService.getStats(),
                migrationEngine.getStats(),
                realtimeService.getStats()
            ]);
            
            const responseTime = Date.now() - startTime;
            
            if (responseTime > 1000) {
                throw new Error(`Performance test failed: ${responseTime}ms > 1000ms`);
            }
            
            console.log(`✅ Service response time: ${responseTime}ms`);

            // Test realtime message performance
            const realtimeStartTime = Date.now();
            const testMigrationId = 'perf_test_' + Date.now();
            
            realtimeService.createMigrationChannel(testMigrationId);
            await realtimeService.broadcastStatusChange(testMigrationId, 'running');
            await realtimeService.cleanupChannel(testMigrationId);
            
            const realtimeTime = Date.now() - realtimeStartTime;
            console.log(`✅ Realtime performance: ${realtimeTime}ms`);

            this.testResults.passed++;

        } catch (error) {
            console.error('❌ Performance test failed:', error.message);
            this.testResults.failed++;
            this.testResults.errors.push(`Performance: ${error.message}`);
        }
    }

    /**
     * Cleanup test data
     */
    async cleanup() {
        console.log('\n🧹 Cleaning up test data...');
        
        try {
            // Cleanup test session
            if (this.testData.sessionId) {
                await supabaseClient
                    .from('scan_sessions')
                    .delete()
                    .eq('id', this.testData.sessionId);
                console.log('✅ Test session cleaned up');
            }

            // Cleanup test users
            await supabaseClient
                .from('users')
                .delete()
                .like('email_google', '<EMAIL>');
            console.log('✅ Test users cleaned up');

            // Cleanup realtime channels
            await realtimeService.cleanupAllChannels();
            console.log('✅ Realtime channels cleaned up');

        } catch (error) {
            console.warn('⚠️ Cleanup warning:', error.message);
        }
    }

    /**
     * Print test summary
     */
    printTestSummary() {
        console.log('\n' + '=' .repeat(60));
        console.log('📊 INTEGRATION TEST SUMMARY');
        console.log('=' .repeat(60));
        
        console.log(`✅ Tests Passed: ${this.testResults.passed}`);
        console.log(`❌ Tests Failed: ${this.testResults.failed}`);
        console.log(`📈 Success Rate: ${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(1)}%`);
        
        if (this.testResults.errors.length > 0) {
            console.log('\n❌ Errors:');
            this.testResults.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error}`);
            });
        }
        
        console.log('\n' + '=' .repeat(60));
        
        if (this.testResults.failed === 0) {
            console.log('🎉 ALL INTEGRATION TESTS PASSED!');
            console.log('✅ System is ready for production deployment');
        } else {
            console.log('⚠️ Some tests failed. Please review and fix issues.');
        }
    }
}

// Run integration tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const testSuite = new IntegrationTestSuite();
    
    testSuite.runAllTests()
        .then(async () => {
            await testSuite.cleanup();
            process.exit(testSuite.testResults.failed === 0 ? 0 : 1);
        })
        .catch(async (error) => {
            console.error('❌ Integration test suite failed:', error);
            await testSuite.cleanup();
            process.exit(1);
        });
}

export { IntegrationTestSuite };
