import { googleAuth } from '../src/auth/google-auth.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Test Google Service Account Authentication
 */
async function testGoogleAuth() {
    console.log('🔍 Testing Google Service Account Authentication...\n');
    console.log('='.repeat(60));

    try {
        // 1. Validate credentials
        console.log('\n1. Validating Service Account credentials...');
        const isValid = googleAuth.validateCredentials();

        if (isValid) {
            console.log('✅ Service Account credentials are valid');
            console.log(`📧 Service Account: ${googleAuth.serviceAccountEmail}`);
            console.log(`🆔 Project ID: ${googleAuth.projectId}`);
        } else {
            console.log('❌ Service Account credentials are invalid');
            return;
        }

        // 2. Test với admin email (từ command line argument)
        const adminEmail = process.argv[2];

        if (!adminEmail) {
            console.log('\n⚠️ No admin email provided. Please provide as command line argument:');
            console.log('Usage: node test-google-auth.js <EMAIL>');
            console.log('Skipping connection tests...');
            return;
        }

        console.log(`\n2. Testing connection with admin email: ${adminEmail}`);
        const connectionResult = await googleAuth.testConnection(adminEmail);

        if (connectionResult.success) {
            console.log('✅ Connection test successful!');
            console.log(`👤 Connected as: ${connectionResult.connectedAs}`);
            console.log(`🔑 Permissions: ${connectionResult.permissions.join(', ')}`);

            if (connectionResult.storageQuota) {
                const quota = connectionResult.storageQuota;
                console.log(`💾 Storage: ${formatBytes(quota.usage)} / ${formatBytes(quota.limit)}`);
            }
        } else {
            console.log('❌ Connection test failed');
            connectionResult.errors.forEach(error => {
                console.log(`   ❌ ${error}`);
            });
        }

        // 3. Test Domain-wide Delegation
        console.log('\n3. Testing Domain-wide Delegation...');
        const delegationResult = await googleAuth.validateDomainWideDelegation(adminEmail);

        if (delegationResult.success) {
            console.log('✅ Domain-wide delegation is working correctly');
        } else {
            console.log('❌ Domain-wide delegation test failed');
            delegationResult.errors.forEach(error => {
                console.log(`   ❌ ${error}`);
            });

            console.log('\n📋 Domain-wide Delegation Setup Instructions:');
            console.log('1. Go to Google Admin Console > Security > API Controls');
            console.log('2. Click "Manage Domain Wide Delegation"');
            console.log('3. Add new API client with:');
            console.log(`   Client ID: ${googleAuth.clientId}`);
            console.log('   OAuth Scopes:');
            googleAuth.defaultScopes.forEach(scope => {
                console.log(`     - ${scope}`);
            });
        }

        // 4. Test caching
        console.log('\n4. Testing authentication caching...');

        const startTime = Date.now();
        await googleAuth.getDriveClient(adminEmail);
        const firstCallTime = Date.now() - startTime;

        const startTime2 = Date.now();
        await googleAuth.getDriveClient(adminEmail); // Should use cache
        const secondCallTime = Date.now() - startTime2;

        console.log(`⏱️ First call: ${firstCallTime}ms`);
        console.log(`⏱️ Second call (cached): ${secondCallTime}ms`);
        console.log(`🚀 Cache speedup: ${Math.round(firstCallTime / secondCallTime)}x faster`);

        // 5. Cache statistics
        const cacheStats = googleAuth.getCacheStats();
        console.log('\n5. Cache statistics:');
        console.log(`📊 Auth clients cached: ${cacheStats.authClients}`);
        console.log(`🎫 Tokens cached: ${cacheStats.tokens}`);

        // 6. Test multiple users (if provided as additional arguments)
        const testUsers = process.argv.slice(3) || []; // Additional emails after the first one

        if (testUsers.length > 0) {
            console.log('\n6. Testing multiple users...');

            for (const userEmail of testUsers) {
                const trimmedEmail = userEmail.trim();
                console.log(`\n   Testing: ${trimmedEmail}`);

                const userResult = await googleAuth.testConnection(trimmedEmail);
                if (userResult.success) {
                    console.log(`   ✅ ${trimmedEmail}: Connected as ${userResult.connectedAs}`);
                } else {
                    console.log(`   ❌ ${trimmedEmail}: Failed`);
                    userResult.errors.forEach(error => {
                        console.log(`      - ${error}`);
                    });
                }
            }
        }

        // 7. Test error handling
        console.log('\n7. Testing error handling...');

        try {
            await googleAuth.testConnection('<EMAIL>');
        } catch (error) {
            console.log('✅ Error handling works correctly for invalid emails');
        }

        console.log('\n🎉 Google Auth testing completed!');

    } catch (error) {
        console.error('❌ Google Auth test failed:', error);

        // Provide helpful error messages
        if (error.message.includes('private_key')) {
            console.log('\n💡 Tip: Check your GOOGLE_PRIVATE_KEY in .env file');
            console.log('   Make sure it includes the full key with -----BEGIN/END PRIVATE KEY-----');
        }

        if (error.message.includes('subject')) {
            console.log('\n💡 Tip: Domain-wide delegation might not be set up correctly');
            console.log('   Check Google Admin Console > Security > API Controls');
        }
    }
}

/**
 * Test specific Google Drive operations
 */
async function testDriveOperations() {
    console.log('\n🔍 Testing Google Drive Operations...\n');

    const testEmail = process.argv[2];

    if (!testEmail) {
        console.log('⚠️ No test email provided. Skipping Drive operations test.');
        console.log('Usage: node test-google-auth.js <EMAIL>');
        return;
    }

    try {
        const drive = await googleAuth.getDriveClient(testEmail);

        // Test 1: List files
        console.log('1. Testing files.list...');
        const filesResponse = await drive.files.list({
            pageSize: 5,
            fields: 'files(id,name,mimeType,size,parents,permissions)',
            q: "trashed=false"
        });

        console.log(`✅ Found ${filesResponse.data.files.length} files`);
        filesResponse.data.files.forEach(file => {
            console.log(`   📄 ${file.name} (${file.mimeType})`);
        });

        // Test 2: Get file details
        if (filesResponse.data.files.length > 0) {
            const testFile = filesResponse.data.files[0];
            console.log(`\n2. Testing files.get for: ${testFile.name}`);

            const fileDetails = await drive.files.get({
                fileId: testFile.id,
                fields: 'id,name,mimeType,size,parents,owners,permissions,webViewLink'
            });

            console.log('✅ File details retrieved:');
            console.log(`   📄 Name: ${fileDetails.data.name}`);
            console.log(`   📏 Size: ${formatBytes(fileDetails.data.size)}`);
            console.log(`   👤 Owner: ${fileDetails.data.owners?.[0]?.emailAddress}`);
            console.log(`   🔗 Link: ${fileDetails.data.webViewLink}`);

            // Test 3: List permissions
            console.log('\n3. Testing permissions.list...');
            const permissionsResponse = await drive.permissions.list({
                fileId: testFile.id,
                fields: 'permissions(id,type,role,emailAddress,domain)'
            });

            console.log(`✅ Found ${permissionsResponse.data.permissions.length} permissions`);
            permissionsResponse.data.permissions.forEach(perm => {
                const identifier = perm.emailAddress || perm.domain || perm.type;
                console.log(`   🔑 ${identifier}: ${perm.role} (${perm.type})`);
            });
        }

        // Test 4: About API
        console.log('\n4. Testing about.get...');
        const aboutResponse = await drive.about.get({
            fields: 'user,storageQuota,canCreateDrives,maxImportSizes'
        });

        console.log('✅ About info retrieved:');
        console.log(`   👤 User: ${aboutResponse.data.user.emailAddress}`);
        console.log(`   💾 Storage: ${formatBytes(aboutResponse.data.storageQuota.usage)} / ${formatBytes(aboutResponse.data.storageQuota.limit)}`);
        console.log(`   📁 Can create drives: ${aboutResponse.data.canCreateDrives}`);

        console.log('\n🎉 Drive operations testing completed!');

    } catch (error) {
        console.error('❌ Drive operations test failed:', error);
    }
}

/**
 * Format bytes to human readable string
 */
function formatBytes(bytes) {
    if (!bytes || bytes === '0') return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 Starting Google Authentication Tests\n');

    await testGoogleAuth();
    await testDriveOperations();

    console.log('\n' + '='.repeat(60));
    console.log('✅ All Google Auth tests completed!');

    // Show cache stats
    const cacheStats = googleAuth.getCacheStats();
    console.log(`\n📊 Final cache stats: ${cacheStats.authClients} clients, ${cacheStats.tokens} tokens`);

    process.exit(0);
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(error => {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    });
}

export { testGoogleAuth, testDriveOperations };
