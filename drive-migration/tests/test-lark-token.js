import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 Testing Lark Token Acquisition...\n');

try {
    const { larkAuth } = await import('./auth/lark-auth.js');
    
    console.log('✅ Lark Auth module loaded');
    console.log('📱 App ID:', larkAuth.appId);
    
    // Test token acquisition
    console.log('\n🎫 Testing token acquisition...');
    const startTime = Date.now();
    
    const token = await larkAuth.getTenantAccessToken();
    const duration = Date.now() - startTime;
    
    console.log('✅ Token acquired successfully!');
    console.log('⏱️ Duration:', duration + 'ms');
    console.log('🎫 Token length:', token.length, 'characters');
    console.log('🎫 Token preview:', token.substring(0, 20) + '...');
    
    // Test token info
    const tokenInfo = larkAuth.getTokenInfo();
    console.log('\n📊 Token Info:');
    console.log('⏰ Expires at:', tokenInfo.expiresAt);
    console.log('⏳ Time to expiry:', tokenInfo.timeToExpiryFormatted);
    console.log('✅ Is valid:', tokenInfo.isValid);
    
    // Test caching
    console.log('\n🚀 Testing token caching...');
    const startTime2 = Date.now();
    await larkAuth.getTenantAccessToken(); // Should use cache
    const cachedDuration = Date.now() - startTime2;
    
    console.log('⏱️ Cached call duration:', cachedDuration + 'ms');
    console.log('🚀 Speedup:', Math.round(duration / cachedDuration) + 'x faster');
    
    // Test API connection
    console.log('\n🔗 Testing API connection...');
    const connectionResult = await larkAuth.testConnection();
    
    if (connectionResult.success) {
        console.log('✅ API connection successful!');
        console.log('📊 Response time:', connectionResult.responseTime + 'ms');
        console.log('🏢 Tenant info:', JSON.stringify(connectionResult.tenantInfo, null, 2));
    } else {
        console.log('❌ API connection failed:', connectionResult.error);
    }
    
    console.log('\n🎉 Lark token testing completed successfully!');
    
} catch (error) {
    console.error('❌ Lark token test failed:', error.message);
    console.error('Stack:', error.stack);
}
