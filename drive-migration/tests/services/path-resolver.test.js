/**
 * Unit Tests cho Path Resolver Service
 * Test tất cả functionality của path-resolver.js
 */

import { test, describe, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert';
import { PathResolver } from '../../src/services/path-resolver.js';
import { TestUtils, TestAssertions, mockData } from '../test-config.js';
import { googleDriveMock } from '../mocks/google-drive-mock.js';

describe('PathResolver Service Tests', () => {
    let pathResolver;
    let testUserEmail;
    
    beforeEach(() => {
        // Reset mocks
        googleDriveMock.reset();
        
        // Create path resolver instance với mocked dependencies
        pathResolver = new PathResolver();
        pathResolver.driveAPI = googleDriveMock;
        
        testUserEmail = TestUtils.generateEmail();
    });
    
    afterEach(() => {
        googleDriveMock.clearCallHistory();
        pathResolver.clearCache();
    });

    describe('Constructor và Configuration', () => {
        test('should initialize với default configuration', () => {
            const resolver = new PathResolver();
            
            assert(resolver.pathToIdCache instanceof Map);
            assert(resolver.idToPathCache instanceof Map);
            assert(resolver.folderHierarchy instanceof Map);
            assert(resolver.cacheTimestamps instanceof Map);
            assert.strictEqual(resolver.cacheTTL, 5 * 60 * 1000); // 5 minutes
        });
        
        test('should start với empty cache', () => {
            const resolver = new PathResolver();
            
            assert.strictEqual(resolver.pathToIdCache.size, 0);
            assert.strictEqual(resolver.idToPathCache.size, 0);
            assert.strictEqual(resolver.cacheTimestamps.size, 0);
        });
    });

    describe('resolvePath Method', () => {
        test('should resolve root path correctly', async () => {
            const result = await pathResolver.resolvePath(testUserEmail, '/');
            
            assert.strictEqual(result, 'root');
        });
        
        test('should resolve empty path as root', async () => {
            const result = await pathResolver.resolvePath(testUserEmail, '');
            
            assert.strictEqual(result, 'root');
        });
        
        test('should resolve simple folder path', async () => {
            // Setup mock folder
            const mockFolder = TestUtils.createMockFolder();
            mockFolder.name = 'Documents';
            mockFolder.parents = ['root'];
            googleDriveMock.addMockFolder(mockFolder);
            
            const result = await pathResolver.resolvePath(testUserEmail, '/Documents');
            
            assert.strictEqual(result, mockFolder.id);
            
            // Verify API was called
            const apiHistory = googleDriveMock.getCallHistory();
            const listCalls = apiHistory.filter(call => call.method === 'listFiles');
            assert(listCalls.length > 0);
        });
        
        test('should resolve nested folder path', async () => {
            // Setup mock folder hierarchy
            const documentsFolder = TestUtils.createMockFolder();
            documentsFolder.name = 'Documents';
            documentsFolder.parents = ['root'];
            
            const projectsFolder = TestUtils.createMockFolder();
            projectsFolder.name = 'Projects';
            projectsFolder.parents = [documentsFolder.id];
            
            googleDriveMock.addMockFolder(documentsFolder);
            googleDriveMock.addMockFolder(projectsFolder);
            
            const result = await pathResolver.resolvePath(testUserEmail, '/Documents/Projects');
            
            assert.strictEqual(result, projectsFolder.id);
        });
        
        test('should use cache for repeated requests', async () => {
            const mockFolder = TestUtils.createMockFolder();
            mockFolder.name = 'TestFolder';
            mockFolder.parents = ['root'];
            googleDriveMock.addMockFolder(mockFolder);
            
            // First call
            const result1 = await pathResolver.resolvePath(testUserEmail, '/TestFolder');
            
            // Clear API history
            googleDriveMock.clearCallHistory();
            
            // Second call should use cache
            const result2 = await pathResolver.resolvePath(testUserEmail, '/TestFolder');
            
            assert.strictEqual(result1, result2);
            
            // Verify no additional API calls were made
            const apiHistory = googleDriveMock.getCallHistory();
            assert.strictEqual(apiHistory.length, 0);
        });
        
        test('should handle folder not found error', async () => {
            await assert.rejects(
                pathResolver.resolvePath(testUserEmail, '/NonExistentFolder'),
                /Folder not found: NonExistentFolder/
            );
        });
        
        test('should normalize paths correctly', async () => {
            const mockFolder = TestUtils.createMockFolder();
            mockFolder.name = 'TestFolder';
            mockFolder.parents = ['root'];
            googleDriveMock.addMockFolder(mockFolder);
            
            // Test various path formats
            const paths = [
                '/TestFolder',
                '/TestFolder/',
                'TestFolder',
                '//TestFolder//',
                '/TestFolder///'
            ];
            
            const results = [];
            for (const path of paths) {
                const result = await pathResolver.resolvePath(testUserEmail, path);
                results.push(result);
            }
            
            // All should resolve to same folder ID
            assert(results.every(result => result === mockFolder.id));
        });
    });

    describe('resolveId Method', () => {
        test('should resolve root ID correctly', async () => {
            const result = await pathResolver.resolveId(testUserEmail, 'root');
            
            assert.strictEqual(result, '/');
        });
        
        test('should resolve simple folder ID', async () => {
            const mockFolder = TestUtils.createMockFolder();
            mockFolder.name = 'Documents';
            mockFolder.parents = ['root'];
            googleDriveMock.addMockFolder(mockFolder);
            
            const result = await pathResolver.resolveId(testUserEmail, mockFolder.id);
            
            assert.strictEqual(result, '/Documents');
        });
        
        test('should resolve nested folder ID', async () => {
            // Setup mock folder hierarchy
            const documentsFolder = TestUtils.createMockFolder();
            documentsFolder.name = 'Documents';
            documentsFolder.parents = ['root'];
            
            const projectsFolder = TestUtils.createMockFolder();
            projectsFolder.name = 'Projects';
            projectsFolder.parents = [documentsFolder.id];
            
            googleDriveMock.addMockFolder(documentsFolder);
            googleDriveMock.addMockFolder(projectsFolder);
            
            const result = await pathResolver.resolveId(testUserEmail, projectsFolder.id);
            
            assert.strictEqual(result, '/Documents/Projects');
        });
        
        test('should use cache for repeated requests', async () => {
            const mockFolder = TestUtils.createMockFolder();
            mockFolder.name = 'TestFolder';
            mockFolder.parents = ['root'];
            googleDriveMock.addMockFolder(mockFolder);
            
            // First call
            const result1 = await pathResolver.resolveId(testUserEmail, mockFolder.id);
            
            // Clear API history
            googleDriveMock.clearCallHistory();
            
            // Second call should use cache
            const result2 = await pathResolver.resolveId(testUserEmail, mockFolder.id);
            
            assert.strictEqual(result1, result2);
            
            // Verify no additional API calls were made
            const apiHistory = googleDriveMock.getCallHistory();
            assert.strictEqual(apiHistory.length, 0);
        });
        
        test('should handle folder not found error', async () => {
            await assert.rejects(
                pathResolver.resolveId(testUserEmail, 'non-existent-id'),
                /Folder not found: non-existent-id/
            );
        });
        
        test('should prevent infinite loops', async () => {
            // Create circular reference (should not happen in real Drive)
            const folder1 = TestUtils.createMockFolder();
            folder1.name = 'Folder1';
            folder1.parents = ['folder2-id'];
            
            const folder2 = TestUtils.createMockFolder();
            folder2.name = 'Folder2';
            folder2.parents = [folder1.id];
            folder2.id = 'folder2-id';
            
            googleDriveMock.addMockFolder(folder1);
            googleDriveMock.addMockFolder(folder2);
            
            await assert.rejects(
                pathResolver.resolveId(testUserEmail, folder1.id),
                /Path depth exceeded maximum limit/
            );
        });
    });

    describe('listFolders Method', () => {
        test('should list folders in root directory', async () => {
            // Add mock folders to root
            for (let i = 0; i < 3; i++) {
                const folder = TestUtils.createMockFolder();
                folder.name = `Folder${i + 1}`;
                folder.parents = ['root'];
                googleDriveMock.addMockFolder(folder);
            }
            
            const folders = await pathResolver.listFolders(testUserEmail, 'root');
            
            assert(Array.isArray(folders));
            assert(folders.length >= 3);
            
            // Verify all returned items are folders
            folders.forEach(folder => {
                TestAssertions.assertObjectHasKeys(folder, ['id', 'name']);
            });
        });
        
        test('should list folders in specific directory', async () => {
            const parentFolder = TestUtils.createMockFolder();
            parentFolder.name = 'ParentFolder';
            parentFolder.parents = ['root'];
            
            const childFolder = TestUtils.createMockFolder();
            childFolder.name = 'ChildFolder';
            childFolder.parents = [parentFolder.id];
            
            googleDriveMock.addMockFolder(parentFolder);
            googleDriveMock.addMockFolder(childFolder);
            
            const folders = await pathResolver.listFolders(testUserEmail, parentFolder.id);
            
            assert(Array.isArray(folders));
            assert(folders.some(folder => folder.id === childFolder.id));
        });
        
        test('should handle empty directory', async () => {
            const emptyFolder = TestUtils.createMockFolder();
            googleDriveMock.addMockFolder(emptyFolder);
            
            const folders = await pathResolver.listFolders(testUserEmail, emptyFolder.id);
            
            assert(Array.isArray(folders));
            assert.strictEqual(folders.length, 0);
        });
    });

    describe('getFolderTree Method', () => {
        test('should build folder tree from root', async () => {
            // Setup mock folder hierarchy
            const folder1 = TestUtils.createMockFolder();
            folder1.name = 'Folder1';
            folder1.parents = ['root'];
            
            const subfolder = TestUtils.createMockFolder();
            subfolder.name = 'Subfolder';
            subfolder.parents = [folder1.id];
            
            googleDriveMock.addMockFolder(folder1);
            googleDriveMock.addMockFolder(subfolder);
            
            const tree = await pathResolver.getFolderTree(testUserEmail, 'root', 3);
            
            assert(tree);
            assert.strictEqual(tree.id, 'root');
            assert.strictEqual(tree.name, 'My Drive');
            assert.strictEqual(tree.depth, 0);
            assert(Array.isArray(tree.children));
        });
        
        test('should respect maxDepth limit', async () => {
            const tree = await pathResolver.getFolderTree(testUserEmail, 'root', 1);
            
            assert(tree);
            assert.strictEqual(tree.depth, 0);
            
            // Children should not have their own children due to depth limit
            if (tree.children.length > 0) {
                tree.children.forEach(child => {
                    assert.strictEqual(child.children.length, 0);
                });
            }
        });
        
        test('should handle non-existent folder', async () => {
            await assert.rejects(
                pathResolver.getFolderTree(testUserEmail, 'non-existent-id', 3),
                /Failed to build folder tree/
            );
        });
    });

    describe('Cache Management', () => {
        test('should update cache correctly', () => {
            const key = `${testUserEmail}:/test/path`;
            const value = 'folder-id-123';
            const reverseKey = '/test/path';
            
            pathResolver.updateCache(key, value, reverseKey);
            
            assert.strictEqual(pathResolver.pathToIdCache.get(key), value);
            assert(pathResolver.cacheTimestamps.has(key));
        });
        
        test('should validate cache TTL', () => {
            const key = `${testUserEmail}:/test/path`;
            const value = 'folder-id-123';
            
            // Add entry to cache
            pathResolver.updateCache(key, value);
            
            // Should be valid immediately
            assert.strictEqual(pathResolver.isCacheValid(key), true);
            
            // Simulate expired cache
            const expiredTimestamp = Date.now() - (pathResolver.cacheTTL + 1000);
            pathResolver.cacheTimestamps.set(key, expiredTimestamp);
            
            // Should be invalid now
            assert.strictEqual(pathResolver.isCacheValid(key), false);
        });
        
        test('should clear cache completely', () => {
            // Add some entries
            pathResolver.updateCache('key1', 'value1');
            pathResolver.updateCache('key2', 'value2');
            
            assert(pathResolver.pathToIdCache.size > 0);
            assert(pathResolver.cacheTimestamps.size > 0);
            
            pathResolver.clearCache();
            
            assert.strictEqual(pathResolver.pathToIdCache.size, 0);
            assert.strictEqual(pathResolver.idToPathCache.size, 0);
            assert.strictEqual(pathResolver.cacheTimestamps.size, 0);
            assert.strictEqual(pathResolver.folderHierarchy.size, 0);
        });
        
        test('should return correct cache statistics', () => {
            // Add some entries
            pathResolver.updateCache('key1', 'value1');
            pathResolver.updateCache('key2', 'value2');
            
            const stats = pathResolver.getCacheStats();
            
            TestAssertions.assertObjectHasKeys(stats, [
                'pathToIdEntries',
                'idToPathEntries', 
                'totalEntries',
                'cacheTTL'
            ]);
            
            assert(stats.pathToIdEntries >= 0);
            assert(stats.idToPathEntries >= 0);
            assert(stats.totalEntries >= 0);
            assert.strictEqual(stats.cacheTTL, pathResolver.cacheTTL);
        });
    });

    describe('Path Normalization', () => {
        test('should normalize various path formats', () => {
            const testCases = [
                { input: '/', expected: '/' },
                { input: '', expected: '/' },
                { input: '/folder', expected: '/folder' },
                { input: 'folder', expected: '/folder' },
                { input: '/folder/', expected: '/folder' },
                { input: '//folder//', expected: '/folder' },
                { input: '/folder1/folder2', expected: '/folder1/folder2' },
                { input: 'folder1/folder2/', expected: '/folder1/folder2' },
                { input: '///folder1///folder2///', expected: '/folder1/folder2' }
            ];
            
            testCases.forEach(({ input, expected }) => {
                const result = pathResolver.normalizePath(input);
                assert.strictEqual(result, expected, `Failed for input: "${input}"`);
            });
        });
        
        test('should handle edge cases', () => {
            assert.strictEqual(pathResolver.normalizePath(null), '/');
            assert.strictEqual(pathResolver.normalizePath(undefined), '/');
            assert.strictEqual(pathResolver.normalizePath('   '), '/');
        });
    });

    describe('Error Handling', () => {
        test('should handle Google Drive API errors', async () => {
            // Simulate API error
            const restoreError = googleDriveMock.simulateError(
                'listFiles',
                new Error('API quota exceeded')
            );
            
            try {
                await assert.rejects(
                    pathResolver.listFolders(testUserEmail, 'root'),
                    /Failed to list folders/
                );
            } finally {
                restoreError();
            }
        });
        
        test('should handle network timeouts', async () => {
            // Simulate network error
            const restoreError = googleDriveMock.simulateNetworkError('getFile', 1.0);
            
            try {
                await assert.rejects(
                    pathResolver.resolveId(testUserEmail, 'some-folder-id'),
                    /Network error/
                );
            } finally {
                restoreError();
            }
        });
        
        test('should handle malformed folder data', async () => {
            // Add folder với missing required fields
            const malformedFolder = {
                id: 'malformed-id'
                // Missing name and parents
            };
            googleDriveMock.addMockFolder(malformedFolder);
            
            // Should handle gracefully
            const result = await pathResolver.resolveId(testUserEmail, 'malformed-id');
            assert(typeof result === 'string');
        });
    });

    describe('Performance Tests', () => {
        test('should handle large folder hierarchies', async () => {
            // Create deep folder structure
            let currentParent = 'root';
            const folderIds = [];
            
            for (let i = 0; i < 10; i++) {
                const folder = TestUtils.createMockFolder();
                folder.name = `Level${i}`;
                folder.parents = [currentParent];
                googleDriveMock.addMockFolder(folder);
                folderIds.push(folder.id);
                currentParent = folder.id;
            }
            
            const startTime = Date.now();
            const result = await pathResolver.resolveId(testUserEmail, folderIds[folderIds.length - 1]);
            const duration = Date.now() - startTime;
            
            assert(typeof result === 'string');
            assert(result.includes('Level9'));
            assert(duration < 5000, 'Should complete within 5 seconds');
        });
        
        test('should cache improve performance', async () => {
            const mockFolder = TestUtils.createMockFolder();
            mockFolder.name = 'TestFolder';
            mockFolder.parents = ['root'];
            googleDriveMock.addMockFolder(mockFolder);
            
            // First call (no cache)
            const start1 = Date.now();
            await pathResolver.resolvePath(testUserEmail, '/TestFolder');
            const duration1 = Date.now() - start1;
            
            // Second call (with cache)
            const start2 = Date.now();
            await pathResolver.resolvePath(testUserEmail, '/TestFolder');
            const duration2 = Date.now() - start2;
            
            // Cached call should be significantly faster
            assert(duration2 < duration1, 'Cached call should be faster');
        });
    });
});
