/**
 * Google Drive API Mock
 * Mock implementation cho Google Drive API để testing
 */

import { TestUtils, mockData } from '../test-config.js';

export class GoogleDriveMock {
    constructor() {
        this.files = new Map();
        this.folders = new Map();
        this.permissions = new Map();
        this.callHistory = [];
        
        // Initialize với mock data
        this.initializeMockData();
    }
    
    initializeMockData() {
        // Tạo root folder
        const rootFolder = {
            id: 'root',
            name: 'My Drive',
            mimeType: 'application/vnd.google-apps.folder',
            parents: [],
            createdTime: '2023-01-01T00:00:00.000Z',
            modifiedTime: '2023-01-01T00:00:00.000Z'
        };
        this.folders.set('root', rootFolder);
        
        // Tạo một số test files và folders
        for (let i = 0; i < 5; i++) {
            const file = TestUtils.createMockFile(1024 * (i + 1));
            this.files.set(file.id, file);
            
            const folder = TestUtils.createMockFolder();
            this.folders.set(folder.id, folder);
        }
    }
    
    // Mock Google Drive API methods
    async listFiles(options = {}) {
        this.callHistory.push({ method: 'listFiles', options });
        
        const {
            q = '',
            pageSize = 100,
            pageToken = null,
            fields = 'files(id,name,mimeType,size,parents,createdTime,modifiedTime,webViewLink)'
        } = options;
        
        let allItems = [...this.files.values(), ...this.folders.values()];
        
        // Apply query filter
        if (q) {
            if (q.includes("mimeType='application/vnd.google-apps.folder'")) {
                allItems = [...this.folders.values()];
            } else if (q.includes("mimeType!='application/vnd.google-apps.folder'")) {
                allItems = [...this.files.values()];
            }
            
            if (q.includes('parents in')) {
                const parentMatch = q.match(/parents in '([^']+)'/);
                if (parentMatch) {
                    const parentId = parentMatch[1];
                    allItems = allItems.filter(item => 
                        item.parents && item.parents.includes(parentId)
                    );
                }
            }
            
            if (q.includes('name contains')) {
                const nameMatch = q.match(/name contains '([^']+)'/);
                if (nameMatch) {
                    const searchTerm = nameMatch[1].toLowerCase();
                    allItems = allItems.filter(item =>
                        item.name.toLowerCase().includes(searchTerm)
                    );
                }
            }
        }
        
        // Apply pagination
        const startIndex = pageToken ? parseInt(pageToken) : 0;
        const endIndex = startIndex + pageSize;
        const files = allItems.slice(startIndex, endIndex);
        
        const response = {
            files,
            nextPageToken: endIndex < allItems.length ? endIndex.toString() : null
        };
        
        return { data: response };
    }
    
    async getFile(fileId, options = {}) {
        this.callHistory.push({ method: 'getFile', fileId, options });
        
        const file = this.files.get(fileId) || this.folders.get(fileId);
        if (!file) {
            throw new Error(`File not found: ${fileId}`);
        }
        
        return { data: file };
    }
    
    async downloadFile(fileId, options = {}) {
        this.callHistory.push({ method: 'downloadFile', fileId, options });
        
        const file = this.files.get(fileId);
        if (!file) {
            throw new Error(`File not found: ${fileId}`);
        }
        
        // Mock file content
        const content = Buffer.from(`Mock content for file ${file.name}`);
        
        return {
            data: content,
            headers: {
                'content-type': file.mimeType,
                'content-length': content.length.toString()
            }
        };
    }
    
    async exportFile(fileId, mimeType) {
        this.callHistory.push({ method: 'exportFile', fileId, mimeType });
        
        const file = this.files.get(fileId);
        if (!file) {
            throw new Error(`File not found: ${fileId}`);
        }
        
        // Mock exported content
        const content = Buffer.from(`Exported content for ${file.name} as ${mimeType}`);
        
        return {
            data: content,
            headers: {
                'content-type': mimeType,
                'content-length': content.length.toString()
            }
        };
    }
    
    async getPermissions(fileId) {
        this.callHistory.push({ method: 'getPermissions', fileId });
        
        const permissions = this.permissions.get(fileId) || [
            {
                id: 'owner',
                type: 'user',
                role: 'owner',
                emailAddress: '<EMAIL>'
            }
        ];
        
        return { data: { permissions } };
    }
    
    async createFolder(name, parentId = 'root') {
        this.callHistory.push({ method: 'createFolder', name, parentId });
        
        const folder = {
            id: TestUtils.generateUUID(),
            name,
            mimeType: 'application/vnd.google-apps.folder',
            parents: [parentId],
            createdTime: new Date().toISOString(),
            modifiedTime: new Date().toISOString()
        };
        
        this.folders.set(folder.id, folder);
        return { data: folder };
    }
    
    async uploadFile(name, content, parentId = 'root', mimeType = 'application/octet-stream') {
        this.callHistory.push({ method: 'uploadFile', name, parentId, mimeType });
        
        const file = {
            id: TestUtils.generateUUID(),
            name,
            mimeType,
            size: content.length,
            parents: [parentId],
            createdTime: new Date().toISOString(),
            modifiedTime: new Date().toISOString(),
            webViewLink: `https://drive.google.com/file/d/${TestUtils.generateUUID()}/view`
        };
        
        this.files.set(file.id, file);
        return { data: file };
    }
    
    // Utility methods cho testing
    addMockFile(file) {
        this.files.set(file.id, file);
    }
    
    addMockFolder(folder) {
        this.folders.set(folder.id, folder);
    }
    
    setMockPermissions(fileId, permissions) {
        this.permissions.set(fileId, permissions);
    }
    
    getCallHistory() {
        return [...this.callHistory];
    }
    
    clearCallHistory() {
        this.callHistory = [];
    }
    
    reset() {
        this.files.clear();
        this.folders.clear();
        this.permissions.clear();
        this.callHistory = [];
        this.initializeMockData();
    }
    
    // Simulate API errors
    simulateError(method, error) {
        const originalMethod = this[method];
        this[method] = async (...args) => {
            throw error;
        };
        
        // Return function để restore original method
        return () => {
            this[method] = originalMethod;
        };
    }
    
    // Simulate rate limiting
    simulateRateLimit(method, delayMs = 1000) {
        const originalMethod = this[method];
        this[method] = async (...args) => {
            await TestUtils.delay(delayMs);
            return originalMethod.apply(this, args);
        };
        
        return () => {
            this[method] = originalMethod;
        };
    }
}

// Export singleton instance
export const googleDriveMock = new GoogleDriveMock();

export default GoogleDriveMock;
