import { google } from 'googleapis';
import dotenv from 'dot        // Test 4: Get user info
console.log('\n4. Getting user info...');
const about = await drive.about.get({ fields: 'user,storageQuota' });
console.log(`✅ Connected as: ${about.data.user.emailAddress}`);
console.log(`📊 Storage used: ${formatBytes(about.data.storageQuota.usage || 0)}`);

// Check if we're properly impersonating the user
if (about.data.user.emailAddress !== userEmail) {
    console.log(`⚠️ Expected to connect as ${userEmail}, but connected as ${about.data.user.emailAddress}`);
    console.log(`💡 This suggests domain-wide delegation is not working correctly`);
}
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Simple Google Drive access test
 */
async function simpleTest() {
    console.log('🔧 Simple Google Drive Access Test');
    console.log('='.repeat(50));

    const userEmail = process.argv[2];
    const keyPath = join(__dirname, '..', 'google-service-account.json');

    console.log(`👤 Testing with user: ${userEmail}`);
    console.log(`🔑 Using key file: ${keyPath}`);

    if (!userEmail) {
        console.log('❌ Please provide user email as command line argument');
        console.log('Usage: node simple-drive-test.js <EMAIL>');
        return;
    }

    try {
        console.log('\n1. Testing basic service account auth...');

        // Test 1: Basic auth without delegation
        const basicAuth = new google.auth.GoogleAuth({
            keyFile: keyPath,
            scopes: ['https://www.googleapis.com/auth/drive.readonly']
        });

        const basicClient = await basicAuth.getClient();
        console.log('✅ Basic service account auth successful');

        // Test 2: Domain-wide delegation
        console.log('\n2. Testing domain-wide delegation...');

        const delegatedAuth = new google.auth.GoogleAuth({
            keyFile: keyPath,
            scopes: [
                'https://www.googleapis.com/auth/drive.readonly',
                'https://www.googleapis.com/auth/drive.metadata.readonly'
            ],
            subject: userEmail
        });

        console.log(`   Attempting to impersonate: ${userEmail}`);

        const delegatedClient = await delegatedAuth.getClient();
        console.log('✅ Domain-wide delegation auth successful');

        // Test 3: Create Drive client
        console.log('\n3. Creating Drive client...');
        const drive = google.drive({ version: 'v3', auth: delegatedClient });

        // Test 4: Get user info
        console.log('\n4. Getting user info...');
        const about = await drive.about.get({ fields: 'user,storageQuota' });
        console.log(`✅ Connected as: ${about.data.user.emailAddress}`);
        console.log(`📊 Storage used: ${formatBytes(about.data.storageQuota.usage)}`);

        // Test 5: List files with minimal query
        console.log('\n5. Listing files (basic query)...');
        const basicFiles = await drive.files.list({
            pageSize: 5,
            fields: 'files(id,name,mimeType)',
            q: 'trashed=false'
        });

        const files = basicFiles.data.files || [];
        console.log(`✅ Found ${files.length} files`);

        if (files.length > 0) {
            console.log('📁 Files:');
            files.forEach(file => {
                console.log(`   - ${file.name} (${file.mimeType})`);
            });
        } else {
            console.log('⚠️ No files found. Checking different strategies...');

            // Try different queries
            console.log('\n6. Trying different file queries...');

            // All files (including trashed)
            const allFiles = await drive.files.list({
                pageSize: 5,
                fields: 'files(id,name,mimeType,trashed)'
            });
            console.log(`   All files (inc. trashed): ${allFiles.data.files?.length || 0}`);

            // Root folder only
            const rootFiles = await drive.files.list({
                pageSize: 5,
                fields: 'files(id,name,mimeType)',
                q: "'root' in parents"
            });
            console.log(`   Root folder files: ${rootFiles.data.files?.length || 0}`);

            // Shared drives
            try {
                const sharedDrives = await drive.drives.list();
                console.log(`   Shared drives: ${sharedDrives.data.drives?.length || 0}`);

                if (sharedDrives.data.drives?.length > 0) {
                    const sharedFiles = await drive.files.list({
                        pageSize: 5,
                        fields: 'files(id,name,mimeType)',
                        q: 'trashed=false',
                        supportsAllDrives: true,
                        includeItemsFromAllDrives: true,
                        corpora: 'allDrives'
                    });
                    console.log(`   Files in shared drives: ${sharedFiles.data.files?.length || 0}`);
                }
            } catch (sharedError) {
                console.log(`   Shared drives error: ${sharedError.message}`);
            }
        }

        console.log('\n✅ All tests completed successfully!');

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);

        if (error.message.includes('unauthorized_client')) {
            console.log('\n💡 This error means domain-wide delegation is not configured.');
            console.log('To fix this:');
            console.log('1. Go to Google Cloud Console → IAM & Admin → Service Accounts');
            console.log('2. Click on your service account');
            console.log('3. Go to "Advanced settings" → "Domain-wide delegation"');
            console.log('4. Check "Enable G Suite Domain-wide Delegation"');
            console.log('5. Note the Client ID');
            console.log('6. Go to Google Admin Console → Security → API Controls');
            console.log('7. Click "Manage Domain Wide Delegation"');
            console.log('8. Add the Client ID with these scopes:');
            console.log('   https://www.googleapis.com/auth/drive');
            console.log('   https://www.googleapis.com/auth/drive.file');
            console.log('   https://www.googleapis.com/auth/drive.metadata');
        } else if (error.message.includes('invalid_grant')) {
            console.log('\n💡 This error means the user email is invalid or not in the organization.');
            console.log(`Check if ${userEmail} is a valid user in your organization.`);
        }

        console.error('\nFull error:', error);
    }
}

function formatBytes(bytes) {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

simpleTest();
