#!/bin/bash

# Domain Switching Script
# Usage: ./switch-domain.sh [osp.vn|osp.com.vn]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Function to display usage
usage() {
    echo "Usage: $0 [osp.vn|osp.com.vn]"
    echo ""
    echo "This script switches the domain configuration for the migration tool."
    echo ""
    echo "Options:"
    echo "  osp.vn      - Switch to osp.vn domain"
    echo "  osp.com.vn  - Switch to osp.com.vn domain"
    echo ""
    echo "Examples:"
    echo "  $0 osp.vn"
    echo "  $0 osp.com.vn"
    exit 1
}

# Function to switch domain
switch_domain() {
    local domain=$1
    local env_file=".env.${domain}"
    
    echo "🔧 Switching to domain: $domain"
    
    # Check if template file exists
    if [ ! -f "$env_file" ]; then
        echo "❌ Template file not found: $env_file"
        echo "Please create the template file first."
        exit 1
    fi
    
    # Backup current .env file
    if [ -f ".env" ]; then
        echo "📦 Backing up current .env to .env.backup"
        cp .env .env.backup
    fi
    
    # Copy template to .env
    echo "📋 Copying $env_file to .env"
    cp "$env_file" .env
    
    echo "✅ Domain switched to $domain"
    echo ""
    echo "📋 Current configuration:"
    echo "   Domain: $domain"
    
    # Extract configuration from .env file
    if grep -q "GOOGLE_ADMIN_EMAIL=" .env; then
        admin_email=$(grep "GOOGLE_ADMIN_EMAIL=" .env | cut -d'=' -f2)
        echo "   Admin Email: $admin_email"
    fi
    
    if grep -q "GOOGLE_SERVICE_ACCOUNT_PATH=" .env; then
        service_account_path=$(grep "GOOGLE_SERVICE_ACCOUNT_PATH=" .env | cut -d'=' -f2)
        echo "   Service Account Path: $service_account_path"
    fi
    
    echo ""
    echo "💡 To test the configuration, run:"
    echo "   node test-domain-config.js"
}

# Main script
if [ $# -eq 0 ]; then
    echo "❌ No domain specified"
    usage
fi

domain=$1

case $domain in
    "osp.vn")
        switch_domain "osp.vn"
        ;;
    "osp.com.vn")
        switch_domain "osp.com.vn"
        ;;
    *)
        echo "❌ Invalid domain: $domain"
        usage
        ;;
esac
