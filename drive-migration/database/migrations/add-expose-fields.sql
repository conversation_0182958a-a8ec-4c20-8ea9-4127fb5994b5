-- Migration: Add fields to support Google Docs expose functionality
-- Date: 2025-07-15
-- Description: Add web_view_link and export_links fields to download_items and update scanned_files download_status

-- Add new fields to download_items table
ALTER TABLE download_items 
ADD COLUMN IF NOT EXISTS web_view_link TEXT,
ADD COLUMN IF NOT EXISTS export_links TEXT; -- JSON string of export links

-- Update scanned_files download_status to include 'exposed' status
ALTER TABLE scanned_files 
DROP CONSTRAINT IF EXISTS scanned_files_download_status_check;

ALTER TABLE scanned_files 
ADD CONSTRAINT scanned_files_download_status_check 
CHECK (download_status IN ('not_downloaded', 'downloaded', 'failed', 'exposed'));

-- Add export_links column to scanned_files if it doesn't exist
ALTER TABLE scanned_files 
ADD COLUMN IF NOT EXISTS export_links TEXT; -- JSON string of export links

-- Create index for web_view_link lookups
CREATE INDEX IF NOT EXISTS idx_download_items_web_view_link ON download_items(web_view_link);
CREATE INDEX IF NOT EXISTS idx_scanned_files_export_links ON scanned_files(export_links);

-- Comments
COMMENT ON COLUMN download_items.web_view_link IS 'Google Drive web view link for Google Docs files';
COMMENT ON COLUMN download_items.export_links IS 'JSON string of available export links for Google Docs files';
COMMENT ON COLUMN scanned_files.export_links IS 'JSON string of available export links for Google Docs files';
