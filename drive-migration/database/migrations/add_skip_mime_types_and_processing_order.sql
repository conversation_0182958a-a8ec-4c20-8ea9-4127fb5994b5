-- Migration: Add skip_mime_types and processing_order to download_sessions
-- Date: 2025-01-15

-- Add new columns to download_sessions table
ALTER TABLE download_sessions 
ADD COLUMN IF NOT EXISTS skip_mime_types TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS processing_order TEXT DEFAULT 'created_at' CHECK (processing_order IN ('created_at', 'user_email', 'size_asc', 'size_desc'));

-- Update the view to include new columns
DROP VIEW IF EXISTS download_session_stats;

CREATE OR REPLACE VIEW download_session_stats AS
SELECT 
    ds.id,
    ds.name,
    ds.status,
    ds.total_files,
    ds.downloaded_files,
    ds.failed_files,
    ds.skipped_files,
    ds.total_size,
    ds.downloaded_size,
    ds.skip_mime_types,
    ds.processing_order,
    ds.stop_on_error,
    ds.continue_on_error,
    CASE 
        WHEN ds.total_files > 0 THEN 
            ROUND((ds.downloaded_files::DECIMAL / ds.total_files::DECIMAL) * 100, 2)
        ELSE 0 
    END as progress_percentage,
    CASE 
        WHEN ds.total_size > 0 THEN 
            ROUND((ds.downloaded_size::DECIMAL / ds.total_size::DECIMAL) * 100, 2)
        ELSE 0 
    END as size_progress_percentage,
    ds.started_at,
    ds.completed_at,
    CASE 
        WHEN ds.completed_at IS NOT NULL AND ds.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (ds.completed_at - ds.started_at))::INTEGER
        WHEN ds.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - ds.started_at))::INTEGER
        ELSE NULL
    END as duration_seconds,
    ds.created_at,
    ds.updated_at
FROM download_sessions ds;
