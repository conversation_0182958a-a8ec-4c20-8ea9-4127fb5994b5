{"version": "0.2.0", "configurations": [{"name": "Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/server.js", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": true, "runtimeArgs": ["--experimental-modules"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Domain Delegation Check", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/check-domain-delegation.js", "args": ["<EMAIL>"], "console": "integratedTerminal", "runtimeArgs": ["--experimental-modules"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Troubleshoot Delegation", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/troubleshoot-delegation.js", "args": ["<EMAIL>"], "console": "integratedTerminal", "runtimeArgs": ["--experimental-modules"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Google Auth Test", "type": "node", "request": "launch", "program": "${workspaceFolder}/tests/test-google-auth.js", "console": "integratedTerminal", "runtimeArgs": ["--experimental-modules"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Database Setup", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/check-database-setup.js", "console": "integratedTerminal", "runtimeArgs": ["--experimental-modules"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Infrastructure Check", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/infrastructure-check.js", "console": "integratedTerminal", "runtimeArgs": ["--experimental-modules"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug All Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/tests/run-all-tests.js", "console": "integratedTerminal", "runtimeArgs": ["--experimental-modules"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Current File", "type": "node", "request": "launch", "program": "${file}", "console": "integratedTerminal", "runtimeArgs": ["--experimental-modules"], "skipFiles": ["<node_internals>/**"]}, {"name": "Attach to Process", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": ".", "skipFiles": ["<node_internals>/**"]}]}