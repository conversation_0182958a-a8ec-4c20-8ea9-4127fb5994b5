{"version": "2.0.0", "tasks": [{"label": "Start Server", "type": "shell", "command": "node", "args": ["src/server.js"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Check Domain Delegation", "type": "shell", "command": "node", "args": ["src/check-domain-delegation.js", "<EMAIL>"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Troubleshoot Delegation", "type": "shell", "command": "node", "args": ["src/troubleshoot-delegation.js", "<EMAIL>"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Run All Tests", "type": "shell", "command": "node", "args": ["tests/run-all-tests.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Check Database Setup", "type": "shell", "command": "node", "args": ["src/check-database-setup.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Infrastructure Check", "type": "shell", "command": "node", "args": ["src/infrastructure-check.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "pnpm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Test Google Auth", "type": "shell", "command": "node", "args": ["tests/test-google-auth.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}