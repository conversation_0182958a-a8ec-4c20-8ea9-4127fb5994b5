/**
 * Report Dashboard Component
 * Giao diện quản lý và tạo báo cáo migration
 */

import React, { useState, useEffect } from 'react';
import './ReportDashboard.css';

const ReportDashboard = () => {
    const [reports, setReports] = useState([]);
    const [migrationTasks, setMigrationTasks] = useState([]);
    const [selectedTask, setSelectedTask] = useState('');
    const [selectedFormat, setSelectedFormat] = useState('csv');
    const [isGenerating, setIsGenerating] = useState(false);
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    // Load available reports và migration tasks
    useEffect(() => {
        loadReports();
        loadMigrationTasks();
    }, []);

    const loadReports = async () => {
        try {
            const response = await fetch('/api/reports/list');
            const data = await response.json();
            
            if (data.success) {
                setReports(data.data.reports);
            } else {
                setError('Failed to load reports: ' + data.error);
            }
        } catch (error) {
            setError('Error loading reports: ' + error.message);
        }
    };

    const loadMigrationTasks = async () => {
        try {
            // Mock migration tasks - trong thực tế sẽ call API
            const mockTasks = [
                { id: 'task-1', name: 'Marketing Drive Migration', status: 'completed' },
                { id: 'task-2', name: 'HR Documents Migration', status: 'completed' },
                { id: 'task-3', name: 'Engineering Files Migration', status: 'running' }
            ];
            setMigrationTasks(mockTasks);
        } catch (error) {
            setError('Error loading migration tasks: ' + error.message);
        }
    };

    const loadTaskStats = async (taskId) => {
        if (!taskId) {
            setStats(null);
            return;
        }

        setLoading(true);
        try {
            const response = await fetch(`/api/reports/stats/${taskId}`);
            const data = await response.json();
            
            if (data.success) {
                setStats(data.data);
            } else {
                setError('Failed to load task stats: ' + data.error);
            }
        } catch (error) {
            setError('Error loading task stats: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const generateReport = async () => {
        if (!selectedTask) {
            setError('Please select a migration task');
            return;
        }

        setIsGenerating(true);
        setError('');

        try {
            const response = await fetch('/api/reports/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    migrationTaskId: selectedTask,
                    format: selectedFormat
                })
            });

            const data = await response.json();
            
            if (data.success) {
                // Reload reports list
                await loadReports();
                setError('');
                alert(`${selectedFormat.toUpperCase()} report generated successfully!`);
            } else {
                setError('Failed to generate report: ' + data.error);
            }
        } catch (error) {
            setError('Error generating report: ' + error.message);
        } finally {
            setIsGenerating(false);
        }
    };

    const downloadReport = async (filename) => {
        try {
            const response = await fetch(`/api/reports/download/${filename}`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } else {
                setError('Failed to download report');
            }
        } catch (error) {
            setError('Error downloading report: ' + error.message);
        }
    };

    const cleanupReports = async () => {
        if (!confirm('Are you sure you want to cleanup old reports? This will keep only the 10 most recent reports.')) {
            return;
        }

        try {
            const response = await fetch('/api/reports/cleanup', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ keepCount: 10 })
            });

            const data = await response.json();
            
            if (data.success) {
                await loadReports();
                alert('Old reports cleaned up successfully!');
            } else {
                setError('Failed to cleanup reports: ' + data.error);
            }
        } catch (error) {
            setError('Error cleaning up reports: ' + error.message);
        }
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString();
    };

    return (
        <div className="report-dashboard">
            <div className="dashboard-header">
                <h2>📊 Migration Reports Dashboard</h2>
                <p>Generate and manage migration reports</p>
            </div>

            {error && (
                <div className="error-message">
                    ❌ {error}
                </div>
            )}

            {/* Report Generation Section */}
            <div className="report-generation">
                <h3>🔧 Generate New Report</h3>
                
                <div className="form-group">
                    <label htmlFor="task-select">Migration Task:</label>
                    <select 
                        id="task-select"
                        value={selectedTask} 
                        onChange={(e) => {
                            setSelectedTask(e.target.value);
                            loadTaskStats(e.target.value);
                        }}
                    >
                        <option value="">Select a migration task...</option>
                        {migrationTasks.map(task => (
                            <option key={task.id} value={task.id}>
                                {task.name} ({task.status})
                            </option>
                        ))}
                    </select>
                </div>

                <div className="form-group">
                    <label htmlFor="format-select">Report Format:</label>
                    <select 
                        id="format-select"
                        value={selectedFormat} 
                        onChange={(e) => setSelectedFormat(e.target.value)}
                    >
                        <option value="csv">CSV (Comma Separated Values)</option>
                        <option value="pdf">PDF (Portable Document Format)</option>
                    </select>
                </div>

                <button 
                    onClick={generateReport}
                    disabled={isGenerating || !selectedTask}
                    className="generate-btn"
                >
                    {isGenerating ? '⏳ Generating...' : '📊 Generate Report'}
                </button>
            </div>

            {/* Task Statistics */}
            {stats && (
                <div className="task-stats">
                    <h3>📈 Task Statistics</h3>
                    {loading ? (
                        <div className="loading">Loading statistics...</div>
                    ) : (
                        <div className="stats-grid">
                            <div className="stat-card">
                                <div className="stat-value">{stats.stats.total_files || 0}</div>
                                <div className="stat-label">Total Files</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-value">{stats.stats.completed_files || 0}</div>
                                <div className="stat-label">Completed</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-value">{stats.stats.failed_files || 0}</div>
                                <div className="stat-label">Failed</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-value">{stats.stats.success_rate || 0}%</div>
                                <div className="stat-label">Success Rate</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-value">{formatFileSize(stats.stats.total_size || 0)}</div>
                                <div className="stat-label">Total Size</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-value">{formatFileSize(stats.stats.processed_size || 0)}</div>
                                <div className="stat-label">Processed Size</div>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Available Reports */}
            <div className="available-reports">
                <div className="reports-header">
                    <h3>📋 Available Reports</h3>
                    <button onClick={cleanupReports} className="cleanup-btn">
                        🧹 Cleanup Old Reports
                    </button>
                </div>

                {reports.length === 0 ? (
                    <div className="no-reports">
                        No reports available. Generate your first report above.
                    </div>
                ) : (
                    <div className="reports-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>📄 Filename</th>
                                    <th>📏 Size</th>
                                    <th>📅 Created</th>
                                    <th>🔧 Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {reports.map((report, index) => (
                                    <tr key={index}>
                                        <td>{report.filename}</td>
                                        <td>{formatFileSize(report.size)}</td>
                                        <td>{formatDate(report.created)}</td>
                                        <td>
                                            <button 
                                                onClick={() => downloadReport(report.filename)}
                                                className="download-btn"
                                            >
                                                ⬇️ Download
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ReportDashboard;
