import React, { useState, useRef, useEffect } from 'react';
import './UserSearch.css';

function UserSearch({ value, onChange, placeholder = 'Tìm kiếm...', onFilter }) {
    const [focused, setFocused] = useState(false);
    const [showFilters, setShowFilters] = useState(false);
    const inputRef = useRef(null);

    const handleClear = () => {
        onChange('');
        inputRef.current?.focus();
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
            handleClear();
        }
    };

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (inputRef.current && !inputRef.current.contains(event.target)) {
                setShowFilters(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    return (
        <div className={`user-search ${focused ? 'focused' : ''}`}>
            <div className="search-input-container">
                <div className="search-icon">🔍</div>
                <input
                    ref={inputRef}
                    type="text"
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    onFocus={() => setFocused(true)}
                    onBlur={() => setFocused(false)}
                    onKeyDown={handleKeyDown}
                    placeholder={placeholder}
                    className="search-input"
                />
                {value && (
                    <button
                        className="clear-button"
                        onClick={handleClear}
                        title="Xóa tìm kiếm"
                    >
                        ✕
                    </button>
                )}
                {onFilter && (
                    <button
                        className={`filter-button ${showFilters ? 'active' : ''}`}
                        onClick={() => setShowFilters(!showFilters)}
                        title="Bộ lọc"
                    >
                        ⚙️
                    </button>
                )}
            </div>

            {/* Advanced Filters */}
            {showFilters && onFilter && (
                <div className="search-filters">
                    <div className="filter-group">
                        <label className="filter-label">Trạng thái:</label>
                        <div className="filter-options">
                            <label className="filter-option">
                                <input type="checkbox" /> Hoạt động
                            </label>
                            <label className="filter-option">
                                <input type="checkbox" /> Không hoạt động
                            </label>
                            <label className="filter-option">
                                <input type="checkbox" /> Bị chặn
                            </label>
                        </div>
                    </div>
                    
                    <div className="filter-group">
                        <label className="filter-label">Kích thước dữ liệu:</label>
                        <div className="filter-range">
                            <select className="filter-select">
                                <option value="">Tất cả</option>
                                <option value="small">Dưới 1GB</option>
                                <option value="medium">1GB - 10GB</option>
                                <option value="large">Trên 10GB</option>
                            </select>
                        </div>
                    </div>

                    <div className="filter-actions">
                        <button className="btn-filter btn-apply">Áp dụng</button>
                        <button className="btn-filter btn-reset">Đặt lại</button>
                    </div>
                </div>
            )}
        </div>
    );
}

export default UserSearch;
