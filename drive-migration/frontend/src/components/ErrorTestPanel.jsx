import React from 'react';
import { apiGet, apiPost } from '../utils/apiUtils';

const ErrorTestPanel = ({ onError, onSuccess }) => {
  const testErrors = [
    // {
    //   name: 'Test 400 Bad Request',
    //   action: () => apiPost('/api/test-error/400'),
    //   description: 'Test yêu cầu không hợp lệ'
    // },
    // {
    //   name: 'Test 401 Unauthorized',
    //   action: () => apiGet('/api/test-error/401'),
    //   description: 'Test không có quyền truy cập'
    // },
    // {
    //   name: 'Test 404 Not Found',
    //   action: () => apiGet('/api/test-error/404'),
    //   description: 'Test endpoint không tồn tại'
    // },
    // {
    //   name: 'Test 500 Server Error',
    //   action: () => apiPost('/api/test-error/500'),
    //   description: 'Test lỗi máy chủ nội bộ'
    // },
    // {
    //   name: 'Test 503 Service Unavailable',
    //   action: () => apiGet('/api/test-error/503'),
    //   description: 'Test dịch vụ không khả dụng'
    // },
    // {
    //   name: 'Test Validation Error',
    //   action: () => apiPost('/api/test-error/validation', { email: 'invalid', password: '123' }),
    //   description: 'Test lỗi validation dữ liệu'
    // },
    // {
    //   name: 'Test Random Error',
    //   action: () => apiGet('/api/test-error/random'),
    //   description: 'Test lỗi ngẫu nhiên'
    // },
    // {
    //   name: 'Test Network Error',
    //   action: () => apiGet('http://localhost:9999/api/test'),
    //   description: 'Test lỗi kết nối mạng'
    // },
    // {
    //   name: 'Test Success',
    //   action: () => apiGet('/api/test-error/success'),
    //   description: 'Test phản hồi thành công'
    // }
  ];

  const handleTest = async (test) => {
    try {
      const result = await test.action();
      onSuccess(`${test.name} thành công: ${JSON.stringify(result)}`);
    } catch (error) {
      onError(error, test.name);
    }
  };

  return (
    <div className="error-test-panel">
      <h3>🧪 Test Error Handling</h3>
      <p className="test-description">
        Panel này để test các loại lỗi khác nhau và xem cách hiển thị lỗi.
      </p>

      <div className="test-buttons">
        {testErrors.map((test, index) => (
          <div key={index} className="test-item">
            <button
              onClick={() => handleTest(test)}
              className="btn btn-secondary btn-small"
            >
              {test.name}
            </button>
            <small className="test-desc">{test.description}</small>
          </div>
        ))}
      </div>

      {/* <div className="test-info">
        <h4>Các loại lỗi được test:</h4>
        <ul>
          <li><strong>400 Bad Request:</strong> Yêu cầu không hợp lệ</li>
          <li><strong>401 Unauthorized:</strong> Không có quyền truy cập</li>
          <li><strong>404 Not Found:</strong> Endpoint không tồn tại</li>
          <li><strong>500 Server Error:</strong> Lỗi máy chủ nội bộ</li>
          <li><strong>503 Service Unavailable:</strong> Dịch vụ không khả dụng</li>
          <li><strong>Validation Error:</strong> Dữ liệu không hợp lệ</li>
          <li><strong>Random Error:</strong> Lỗi ngẫu nhiên</li>
          <li><strong>Network Error:</strong> Lỗi kết nối mạng</li>
          <li><strong>Success:</strong> Phản hồi thành công</li>
        </ul>
      </div> */}
    </div>
  );
};

export default ErrorTestPanel;
