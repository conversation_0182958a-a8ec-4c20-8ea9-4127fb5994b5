.users-overview {
  padding: 20px;
}

.user-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.user-card {
  display: flex;
  flex-direction: column;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 8px;
}

.user-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.user-name {
  font-weight: bold;
}

.user-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
