import React, { useState } from 'react';

const TreeView = ({ tree, selectedFiles, onFileSelect, onSelectAll, onSelectAllToggle }) => {
    const [expandedFolders, setExpandedFolders] = useState(new Set());

    const toggleFolder = (folderId) => {
        const newExpanded = new Set(expandedFolders);
        if (newExpanded.has(folderId)) {
            newExpanded.delete(folderId);
        } else {
            newExpanded.add(folderId);
        }
        setExpandedFolders(newExpanded);
    };

    const isFileSelected = (fileId) => {
        return selectedFiles.some(f => f.id === fileId);
    };

    // Helper function to get all files within a folder (recursively)
    const getAllFilesInFolder = (folderNode) => {
        let files = [];
        if (folderNode.children) {
            folderNode.children.forEach(child => {
                if (child.type === 'file') {
                    files.push(child);
                } else if (child.type === 'folder') {
                    files = files.concat(getAllFilesInFolder(child));
                }
            });
        }
        return files;
    };

    // Get folder selection state: 'none', 'some', 'all'
    const getFolderSelectionState = (folderNode) => {
        const allFilesInFolder = getAllFilesInFolder(folderNode);
        if (allFilesInFolder.length === 0) return 'none';
        
        const selectedFilesInFolder = allFilesInFolder.filter(file => 
            selectedFiles.some(sf => sf.id === file.id)
        );
        
        if (selectedFilesInFolder.length === 0) return 'none';
        if (selectedFilesInFolder.length === allFilesInFolder.length) return 'all';
        return 'some';
    };

    // Handle folder selection/deselection
    const handleFolderSelect = (folderNode, shouldSelect) => {
        const allFilesInFolder = getAllFilesInFolder(folderNode);
        
        if (shouldSelect) {
            // Add all files in folder to selection
            const newSelection = [...selectedFiles];
            allFilesInFolder.forEach(file => {
                if (!selectedFiles.some(sf => sf.id === file.id)) {
                    newSelection.push(file);
                }
            });
            onSelectAll(newSelection);
        } else {
            // Remove all files in folder from selection
            const fileIdsInFolder = allFilesInFolder.map(f => f.id);
            const newSelection = selectedFiles.filter(sf => 
                !fileIdsInFolder.includes(sf.id)
            );
            onSelectAll(newSelection);
        }
    };

    const getFileIcon = (mimeType, type) => {
        if (type === 'folder') return '📁';

        if (mimeType?.startsWith('image/')) return '🖼️';
        if (mimeType?.startsWith('video/')) return '🎥';
        if (mimeType?.startsWith('audio/')) return '🎵';
        if (mimeType?.includes('pdf')) return '📄';
        if (mimeType?.includes('document') || mimeType?.includes('word')) return '📝';
        if (mimeType?.includes('spreadsheet') || mimeType?.includes('excel')) return '📊';
        if (mimeType?.includes('presentation') || mimeType?.includes('powerpoint')) return '📈';
        if (mimeType?.includes('zip') || mimeType?.includes('archive')) return '📦';

        return '📄';
    };

    const formatFileSize = (bytes) => {
        if (!bytes) return '0 B';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const getDownloadStatusIcon = (downloadStatus) => {
        switch (downloadStatus) {
            case 'downloaded':
                return '✅';
            case 'failed':
                return '❌';
            case 'not_downloaded':
            case null:
            case undefined:
                return '⏳';
            default:
                return '⏳';
        }
    };

    const getDownloadStatusText = (downloadStatus) => {
        switch (downloadStatus) {
            case 'downloaded':
                return 'Downloaded';
            case 'failed':
                return 'Failed';
            case 'not_downloaded':
            case null:
            case undefined:
                return 'Not Downloaded';
            default:
                return 'Not Downloaded';
        }
    };

    const renderTreeNode = (node, depth = 0) => {
        const isFolder = node.type === 'folder';
        const isExpanded = expandedFolders.has(node.id);
        const hasChildren = node.children && node.children.length > 0;
        const isSelected = !isFolder && isFileSelected(node.id);
        
        // For folders, get selection state
        const folderSelectionState = isFolder ? getFolderSelectionState(node) : null;
        const folderHasFiles = isFolder && getAllFilesInFolder(node).length > 0;

        return (
            <div key={node.id} className="tree-node">
                <div
                    className={`tree-item ${isSelected ? 'selected' : ''} ${folderSelectionState === 'all' ? 'folder-all-selected' : folderSelectionState === 'some' ? 'folder-some-selected' : ''}`}
                    style={{ paddingLeft: `${depth * 20 + 10}px` }}
                >
                    {/* Expand/Collapse button for folders */}
                    {isFolder && (
                        <button
                            className={`tree-toggle ${hasChildren ? '' : 'empty'}`}
                            onClick={() => toggleFolder(node.id)}
                            disabled={!hasChildren}
                        >
                            {hasChildren ? (isExpanded ? '▼' : '▶') : '○'}
                        </button>
                    )}

                    {/* Checkbox for both files and folders */}
                    <label className="tree-checkbox">
                        <input
                            type="checkbox"
                            checked={isFolder ? folderSelectionState === 'all' : isSelected}
                            ref={isFolder ? (el) => {
                                if (el) {
                                    el.indeterminate = folderSelectionState === 'some';
                                }
                            } : null}
                            onChange={(e) => {
                                if (isFolder) {
                                    handleFolderSelect(node, e.target.checked);
                                } else {
                                    onFileSelect(node, e.target.checked);
                                }
                            }}
                            disabled={isFolder && !folderHasFiles}
                        />
                        <span className="checkbox-custom"></span>
                    </label>

                    {/* Icon */}
                    <span className="tree-icon">
                        {getFileIcon(node.mime_type, node.type)}
                    </span>

                    {/* Name */}
                    <span className="tree-name" title={node.name}>
                        {node.name}
                    </span>

                    {/* File info */}
                    <div className="tree-info">
                        {isFolder ? (
                            <span className="folder-stats">
                                {node.fileCount > 0 && `${node.fileCount} files`}
                                {node.fileCount > 0 && node.folderCount > 0 && ', '}
                                {node.folderCount > 0 && `${node.folderCount} folders`}
                                {node.totalSize > 0 && ` (${formatFileSize(node.totalSize)})`}
                                {folderSelectionState === 'some' && (
                                    <span className="selection-indicator"> - Partially Selected</span>
                                )}
                                {folderSelectionState === 'all' && folderHasFiles && (
                                    <span className="selection-indicator"> - All Selected</span>
                                )}
                            </span>
                        ) : (
                            <>
                                <span className="file-size">{formatFileSize(node.size)}</span>
                                {node.modified_time && (
                                    <span className="file-date">
                                        {new Date(node.modified_time).toLocaleDateString()}
                                    </span>
                                )}
                                <span
                                    className={`download-status ${node.download_status || 'not_downloaded'}`}
                                    title={getDownloadStatusText(node.download_status)}
                                >
                                    {getDownloadStatusIcon(node.download_status)}
                                </span>
                            </>
                        )}
                    </div>
                </div>

                {/* Children */}
                {isFolder && isExpanded && hasChildren && (
                    <div className="tree-children">
                        {node.children
                            .sort((a, b) => {
                                // Folders first, then files, both alphabetically (case-insensitive)
                                if (a.type !== b.type) {
                                    return a.type === 'folder' ? -1 : 1;
                                }
                                return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
                            })
                            .map(child => renderTreeNode(child, depth + 1))
                        }
                    </div>
                )}
            </div>
        );
    };

    const getAllFiles = (nodes) => {
        let files = [];
        nodes.forEach(node => {
            if (node.type === 'file') {
                files.push(node);
            }
            if (node.children) {
                files = files.concat(getAllFiles(node.children));
            }
        });
        return files;
    };

    const allFiles = getAllFiles(tree);
    const selectedCount = selectedFiles.length;
    const totalCount = allFiles.length;

    return (
        <div className="tree-view">
            <div className="tree-header">
                <div className="tree-controls">
                    <label className="select-all-checkbox">
                        <input
                            type="checkbox"
                            checked={selectedCount === totalCount && totalCount > 0}
                            onChange={(e) => {
                                const isSelectingAll = e.target.checked;
                                onSelectAll(isSelectingAll ? allFiles : []);
                                
                                // Notify parent about select all action for API call
                                if (onSelectAllToggle) {
                                    onSelectAllToggle(isSelectingAll);
                                }
                            }}
                        />
                        <span className="checkbox-custom"></span>
                        <span>Select All ({totalCount} files)</span>
                    </label>
                </div>

                <div className="tree-stats">
                    <span>Selected: {selectedCount} / {totalCount}</span>
                </div>
            </div>

            <div className="tree-content">
                {tree
                    .sort((a, b) => {
                        // Folders first, then files, both alphabetically (case-insensitive)
                        if (a.type !== b.type) {
                            return a.type === 'folder' ? -1 : 1;
                        }
                        return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
                    })
                    .map(node => renderTreeNode(node))
                }
            </div>
        </div>
    );
};

export default TreeView;
