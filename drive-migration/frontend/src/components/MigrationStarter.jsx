import React, { useState } from 'react';
import MigrationDashboard from './MigrationDashboard';

/**
 * Migration Starter Component
 * Interface để start migration v<PERSON> chuy<PERSON>n sang dashboard
 */
const MigrationStarter = () => {
  const [migrationId, setMigrationId] = useState(null);
  const [isStarting, setIsStarting] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    userEmail: '',
    sessionId: '',
    mapPermissions: true,
    targetRootFolder: '',
    preserveFolderStructure: true
  });

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Start migration
  const startMigration = async () => {
    if (!formData.userEmail || !formData.sessionId) {
      setError('User email and session ID are required');
      return;
    }

    setIsStarting(true);
    setError(null);

    try {
      const response = await fetch('/api/migration/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userEmail: formData.userEmail,
          sessionId: formData.sessionId,
          options: {
            mapPermissions: formData.mapPermissions,
            targetRootFolder: formData.targetRootFolder || null,
            preserveFolderStructure: formData.preserveFolderStructure
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setMigrationId(result.migrationId);
        console.log('✅ Migration started:', result.migrationId);
      } else {
        throw new Error(result.error || 'Failed to start migration');
      }

    } catch (err) {
      console.error('❌ Error starting migration:', err);
      setError(err.message);
    } finally {
      setIsStarting(false);
    }
  };

  // Close dashboard and return to starter
  const closeDashboard = () => {
    setMigrationId(null);
    setFormData({
      userEmail: '',
      sessionId: '',
      mapPermissions: true,
      targetRootFolder: '',
      preserveFolderStructure: true
    });
  };

  // If migration is started, show dashboard
  if (migrationId) {
    return <MigrationDashboard migrationId={migrationId} onClose={closeDashboard} />;
  }

  // Show migration starter form
  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Start Migration</h1>
        
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        <form onSubmit={(e) => { e.preventDefault(); startMigration(); }} className="space-y-6">
          {/* User Email */}
          <div>
            <label htmlFor="userEmail" className="block text-sm font-medium text-gray-700 mb-2">
              User Email *
            </label>
            <input
              type="email"
              id="userEmail"
              name="userEmail"
              value={formData.userEmail}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
            <p className="text-xs text-gray-500 mt-1">Google Drive user email to migrate from</p>
          </div>

          {/* Session ID */}
          <div>
            <label htmlFor="sessionId" className="block text-sm font-medium text-gray-700 mb-2">
              Scan Session ID *
            </label>
            <input
              type="text"
              id="sessionId"
              name="sessionId"
              value={formData.sessionId}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="session-uuid-here"
            />
            <p className="text-xs text-gray-500 mt-1">Session ID from Drive scanning</p>
          </div>

          {/* Target Root Folder */}
          <div>
            <label htmlFor="targetRootFolder" className="block text-sm font-medium text-gray-700 mb-2">
              Target Root Folder (Optional)
            </label>
            <input
              type="text"
              id="targetRootFolder"
              name="targetRootFolder"
              value={formData.targetRootFolder}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Migration_2025"
            />
            <p className="text-xs text-gray-500 mt-1">Folder name in Lark Drive (leave empty for root)</p>
          </div>

          {/* Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Migration Options</h3>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="mapPermissions"
                name="mapPermissions"
                checked={formData.mapPermissions}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="mapPermissions" className="ml-2 block text-sm text-gray-900">
                Map file permissions
              </label>
            </div>
            <p className="text-xs text-gray-500 ml-6">Transfer Google Drive permissions to Lark Drive</p>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="preserveFolderStructure"
                name="preserveFolderStructure"
                checked={formData.preserveFolderStructure}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="preserveFolderStructure" className="ml-2 block text-sm text-gray-900">
                Preserve folder structure
              </label>
            </div>
            <p className="text-xs text-gray-500 ml-6">Recreate Google Drive folder hierarchy in Lark</p>
          </div>

          {/* Submit Button */}
          <div className="pt-6">
            <button
              type="submit"
              disabled={isStarting}
              className={`w-full py-3 px-4 rounded-md font-medium ${
                isStarting
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500'
              } text-white transition-colors`}
            >
              {isStarting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Starting Migration...
                </div>
              ) : (
                'Start Migration'
              )}
            </button>
          </div>
        </form>

        {/* Info Section */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Migration Process</h4>
          <ul className="text-xs text-blue-800 space-y-1">
            <li>• Files will be downloaded from Google Drive</li>
            <li>• Files will be uploaded to Lark Drive</li>
            <li>• Folder structure will be recreated</li>
            <li>• Permissions will be mapped (if enabled)</li>
            <li>• Progress will be tracked in real-time</li>
          </ul>
        </div>

        {/* Test Data Helper */}
        <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Test Data</h4>
          <div className="space-y-2">
            <button
              type="button"
              onClick={() => setFormData(prev => ({
                ...prev,
                userEmail: '<EMAIL>',
                sessionId: 'test-session-123'
              }))}
              className="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
            >
              Fill Test Data
            </button>
            <p className="text-xs text-gray-600">Click to fill form with test data for development</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MigrationStarter;
