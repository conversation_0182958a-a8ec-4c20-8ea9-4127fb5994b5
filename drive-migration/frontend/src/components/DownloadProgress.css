.download-progress {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.download-progress.loading,
.download-progress.error {
    text-align: center;
    padding: 3rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Header */
.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 1rem;
}

.progress-header h2 {
    margin: 0;
    color: #2c3e50;
}

.session-status .status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
}

/* Overall Progress */
.overall-progress {
    margin-bottom: 2rem;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 500;
}

/* Progress Bars */
.progress-bars {
    margin-bottom: 2rem;
}

.progress-bar-container {
    margin-bottom: 1rem;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill.size-progress {
    background: linear-gradient(90deg, #27ae60, #229954);
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Current Activity */
.current-activity {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: #e3f2fd;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid #2196f3;
}

.activity-icon {
    font-size: 1.5rem;
}

.activity-text {
    flex: 1;
}

.current-user {
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 0.25rem;
}

.current-file {
    color: #424242;
    font-size: 0.9rem;
}

/* Session Duration */
.session-duration {
    text-align: center;
    font-weight: 500;
    color: #6c757d;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

/* Status Summary */
.status-summary {
    display: flex;
    justify-content: space-around;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.status-item .status-icon {
    font-size: 1.5rem;
}

.status-count {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
}

.status-text {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 500;
}

/* Control Buttons */
.control-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* Download Items */
.download-items {
    border-top: 1px solid #e9ecef;
    padding-top: 2rem;
}

.items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.items-header h3 {
    margin: 0;
    color: #2c3e50;
}

.items-filter select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

/* Items List */
.items-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.download-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    gap: 1rem;
}

.download-item:last-child {
    border-bottom: none;
}

.download-item:hover {
    background: #f8f9fa;
}

.item-status {
    flex-shrink: 0;
}

.item-status .status-icon {
    font-size: 1.25rem;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.item-path {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.item-user {
    font-size: 0.8rem;
    color: #3498db;
}

.item-details {
    flex-shrink: 0;
    text-align: right;
}

.item-size {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.item-error {
    font-size: 0.8rem;
    color: #e74c3c;
    margin-bottom: 0.25rem;
}

.item-retry {
    font-size: 0.8rem;
    color: #f39c12;
}

.no-items {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

/* Status Classes */
.status-pending { color: #f39c12; }
.status-downloading { color: #3498db; }
.status-completed { color: #27ae60; }
.status-failed { color: #e74c3c; }
.status-skipped { color: #95a5a6; }
.status-unknown { color: #6c757d; }

.status-badge.status-pending { background: #ffeaa7; color: #d63031; }
.status-badge.status-downloading { background: #74b9ff; color: white; }
.status-badge.status-completed { background: #00b894; color: white; }
.status-badge.status-failed { background: #d63031; color: white; }
.status-badge.status-skipped { background: #636e72; color: white; }

/* Responsive Design */
@media (max-width: 768px) {
    .download-progress {
        padding: 1rem;
    }
    
    .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .progress-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .status-summary {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .control-buttons {
        flex-direction: column;
    }
    
    .items-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .download-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .item-details {
        text-align: left;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .progress-stats {
        grid-template-columns: 1fr;
    }
    
    .status-summary {
        flex-direction: column;
    }
    
    .current-activity {
        flex-direction: column;
        text-align: center;
    }
}
