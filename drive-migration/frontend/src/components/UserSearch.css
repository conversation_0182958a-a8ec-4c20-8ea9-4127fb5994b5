/* User Search Component Styles */
.user-search {
    position: relative;
    min-width: 280px;
    max-width: 400px;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.2s ease;
    overflow: hidden;
}

.user-search.focused .search-input-container {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.search-icon {
    padding: 0 0.75rem;
    color: #a0aec0;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.search-input {
    flex: 1;
    padding: 0.75rem 0.5rem;
    border: none;
    outline: none;
    font-size: 0.9rem;
    background: transparent;
    color: #2d3748;
}

.search-input::placeholder {
    color: #a0aec0;
}

.clear-button,
.filter-button {
    padding: 0.5rem;
    border: none;
    background: transparent;
    color: #a0aec0;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.clear-button:hover,
.filter-button:hover {
    color: #4a5568;
    background: #f7fafc;
}

.filter-button.active {
    color: #4299e1;
    background: #ebf8ff;
}

.search-filters {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin-top: 0.5rem;
    padding: 1rem;
    animation: slideDown 0.2s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group:last-of-type {
    margin-bottom: 1.5rem;
}

.filter-label {
    display: block;
    font-size: 0.85rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #718096;
    cursor: pointer;
    padding: 0.25rem 0;
}

.filter-option input[type="checkbox"] {
    margin: 0;
    accent-color: #4299e1;
}

.filter-option:hover {
    color: #2d3748;
}

.filter-range {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.filter-select {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.85rem;
    background: white;
    color: #4a5568;
}

.filter-select:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
}

.btn-filter {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-apply {
    background: #4299e1;
    color: white;
}

.btn-apply:hover {
    background: #3182ce;
}

.btn-reset {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.btn-reset:hover {
    background: #edf2f7;
    color: #2d3748;
}

/* Responsive Design */
@media (max-width: 768px) {
    .user-search {
        min-width: 200px;
        max-width: 100%;
    }
    
    .search-filters {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        right: auto;
        width: 90%;
        max-width: 400px;
        margin-top: 0;
    }
    
    .filter-options {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .filter-actions {
        flex-direction: column;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .search-input-container {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .search-input {
        color: #e2e8f0;
    }
    
    .search-input::placeholder {
        color: #718096;
    }
    
    .search-filters {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .filter-label {
        color: #e2e8f0;
    }
    
    .filter-option {
        color: #a0aec0;
    }
    
    .filter-option:hover {
        color: #e2e8f0;
    }
    
    .filter-select {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .btn-reset {
        background: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }
}
