/* Report Dashboard Styles */

.report-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-header h2 {
    margin: 0 0 10px 0;
    font-size: 2rem;
    font-weight: 600;
}

.dashboard-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.error-message {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
}

/* Report Generation Section */
.report-generation {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e5e9;
}

.report-generation h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #34495e;
}

.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: border-color 0.2s ease;
}

.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.generate-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Task Statistics */
.task-stats {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e5e9;
}

.task-stats h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #7f8c8d;
    font-style: italic;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #dee2e6;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Available Reports */
.available-reports {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e5e9;
}

.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.reports-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.cleanup-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cleanup-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.no-reports {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
    font-style: italic;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

.reports-table {
    overflow-x: auto;
}

.reports-table table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.reports-table th,
.reports-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.reports-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.reports-table tr:hover {
    background: #f8f9fa;
}

.download-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.download-btn:hover {
    background: #229954;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .report-dashboard {
        padding: 15px;
    }
    
    .dashboard-header h2 {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .reports-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .reports-table {
        font-size: 14px;
    }
    
    .reports-table th,
    .reports-table td {
        padding: 8px 12px;
    }
}

@media (max-width: 480px) {
    .form-group select,
    .generate-btn {
        font-size: 14px;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .stat-value {
        font-size: 1.3rem;
    }
}
