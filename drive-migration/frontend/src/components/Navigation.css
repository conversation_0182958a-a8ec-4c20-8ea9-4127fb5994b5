/* Navigation Component Styles */
.navigation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    height: 70px;
}

/* Brand */
.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
}

.brand-icon {
    font-size: 2rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
    font-weight: 400;
}

/* Navigation Links */
.nav-links {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4fd1c7, #81e6d9);
    border-radius: 3px 3px 0 0;
}

.nav-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.nav-text {
    font-weight: 500;
    white-space: nowrap;
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #4fd1c7, #81e6d9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: #2d3748;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Mobile Menu */
.mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-menu-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.5rem 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.mobile-nav-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.mobile-nav-text {
    flex: 1;
}

.mobile-nav-label {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.mobile-nav-desc {
    font-size: 0.85rem;
    opacity: 0.8;
}

.mobile-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.mobile-action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.mobile-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-container {
        padding: 0 1.5rem;
    }
    
    .nav-links {
        gap: 0.25rem;
    }
    
    .nav-link {
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
        height: 60px;
    }
    
    .nav-links,
    .nav-actions {
        display: none;
    }
    
    .mobile-menu-btn {
        display: flex;
    }
    
    .brand-text {
        display: none;
    }
    
    .brand-icon {
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }
    
    .mobile-menu-content {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        height: 56px;
    }
    
    .brand-icon {
        width: 36px;
        height: 36px;
        font-size: 1.3rem;
    }
    
    .mobile-menu-content {
        padding: 0.75rem;
    }
    
    .mobile-nav-link {
        padding: 0.75rem;
    }
    
    .mobile-nav-icon {
        font-size: 1.3rem;
    }
    
    .mobile-nav-label {
        font-size: 0.9rem;
    }
    
    .mobile-nav-desc {
        font-size: 0.8rem;
    }
}
