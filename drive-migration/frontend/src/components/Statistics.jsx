import React from 'react';

const Statistics = ({ stats, selectedFiles }) => {
  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getSelectedSize = () => {
    return selectedFiles.reduce((total, file) => total + (file.size || 0), 0);
  };

  const getTopFileTypes = () => {
    if (!stats?.fileTypes) return [];
    
    return Object.entries(stats.fileTypes)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([type, count]) => ({
        type: type.split('/').pop() || type,
        count,
        fullType: type
      }));
  };

  if (!stats) {
    return (
      <div className="statistics">
        <div className="stats-loading">
          <div className="spinner"></div>
          <p>Loading statistics...</p>
        </div>
      </div>
    );
  }

  const topFileTypes = getTopFileTypes();
  const selectedSize = getSelectedSize();

  return (
    <div className="statistics">
      <h3>📊 Scan Statistics</h3>
      
      <div className="stats-grid">
        {/* Total counts */}
        <div className="stat-card">
          <div className="stat-icon">📁</div>
          <div className="stat-content">
            <div className="stat-number">{stats.totalFolders.toLocaleString()}</div>
            <div className="stat-label">Folders</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📄</div>
          <div className="stat-content">
            <div className="stat-number">{stats.totalFiles.toLocaleString()}</div>
            <div className="stat-label">Files</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">💾</div>
          <div className="stat-content">
            <div className="stat-number">{formatFileSize(stats.totalSize)}</div>
            <div className="stat-label">Total Size</div>
          </div>
        </div>

        {/* Selection stats */}
        <div className="stat-card selected">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <div className="stat-number">{selectedFiles.length.toLocaleString()}</div>
            <div className="stat-label">Selected Files</div>
          </div>
        </div>

        <div className="stat-card selected">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <div className="stat-number">{formatFileSize(selectedSize)}</div>
            <div className="stat-label">Selected Size</div>
          </div>
        </div>

        <div className="stat-card selected">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <div className="stat-number">
              {stats.totalFiles > 0 ? Math.round((selectedFiles.length / stats.totalFiles) * 100) : 0}%
            </div>
            <div className="stat-label">Selection Rate</div>
          </div>
        </div>
      </div>

      {/* File types breakdown */}
      {topFileTypes.length > 0 && (
        <div className="file-types-section">
          <h4>📋 Top File Types</h4>
          <div className="file-types-list">
            {topFileTypes.map(({ type, count, fullType }) => (
              <div key={fullType} className="file-type-item">
                <div className="file-type-info">
                  <span className="file-type-name" title={fullType}>
                    {type}
                  </span>
                  <span className="file-type-count">{count.toLocaleString()}</span>
                </div>
                <div className="file-type-bar">
                  <div 
                    className="file-type-progress"
                    style={{ 
                      width: `${(count / stats.totalFiles) * 100}%` 
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Statistics;
