import React from 'react';

const ErrorDisplay = ({ 
  error, 
  title = "Đã xảy ra lỗi", 
  onRetry = null, 
  onDismiss = null,
  showDetails = true,
  className = ""
}) => {
  if (!error) return null;

  // Parse error information
  const getErrorInfo = () => {
    if (typeof error === 'string') {
      return { message: error, details: null, code: null };
    }
    
    if (error instanceof Error) {
      return { 
        message: error.message, 
        details: error.stack, 
        code: error.code || null 
      };
    }
    
    // Handle API error responses
    if (error.response) {
      return {
        message: error.response.data?.message || error.response.statusText || 'Lỗi API',
        details: error.response.data?.details || `HTTP ${error.response.status}`,
        code: error.response.status
      };
    }
    
    // Handle fetch errors
    if (error.status) {
      return {
        message: error.message || 'Lỗi kết nối',
        details: `HTTP ${error.status}: ${error.statusText}`,
        code: error.status
      };
    }
    
    return { 
      message: error.message || 'Lỗi không x<PERSON>c đ<PERSON>', 
      details: JSON.stringify(error, null, 2),
      code: null 
    };
  };

  const errorInfo = getErrorInfo();

  return (
    <div className={`error-display ${className}`}>
      <div className="error-content">
        <div className="error-header">
          <div className="error-icon">❌</div>
          <div className="error-title">{title}</div>
          {onDismiss && (
            <button 
              onClick={onDismiss}
              className="error-dismiss"
              aria-label="Đóng"
            >
              ✕
            </button>
          )}
        </div>
        
        <div className="error-body">
          <div className="error-message">
            {errorInfo.message}
          </div>
          
          {errorInfo.code && (
            <div className="error-code">
              Mã lỗi: {errorInfo.code}
            </div>
          )}
          
          {showDetails && errorInfo.details && (
            <details className="error-details">
              <summary>Chi tiết lỗi</summary>
              <pre className="error-details-content">
                {errorInfo.details}
              </pre>
            </details>
          )}
        </div>
        
        {onRetry && (
          <div className="error-actions">
            <button 
              onClick={onRetry}
              className="btn btn-primary btn-small"
            >
              🔄 Thử lại
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ErrorDisplay;
