import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import './DownloadProgress.css';

const DownloadProgress = ({ session, onPause, onCancel, onComplete }) => {
    const [progressData, setProgressData] = useState(null);
    const [downloadItems, setDownloadItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(20);
    const [statusFilter, setStatusFilter] = useState('all');

    useEffect(() => {
        if (session?.id) {
            loadProgress();
            loadDownloadItems();
            
            // Set up polling for real-time updates
            const interval = setInterval(() => {
                loadProgress();
                loadDownloadItems();
            }, 2000);

            return () => clearInterval(interval);
        }
    }, [session?.id, currentPage, statusFilter]);

    useEffect(() => {
        // Check if session is completed
        if (progressData?.session?.status === 'completed' || progressData?.session?.status === 'failed') {
            onComplete?.();
        }
    }, [progressData?.session?.status, onComplete]);

    /**
     * Load progress data
     */
    const loadProgress = async () => {
        try {
            const response = await apiGet(`/api/download/sessions/${session.id}/progress`);
            if (response.success) {
                setProgressData(response.data);
            }
        } catch (error) {
            console.error('Error loading progress:', error);
        } finally {
            setLoading(false);
        }
    };

    /**
     * Load download items
     */
    const loadDownloadItems = async () => {
        try {
            const params = new URLSearchParams({
                page: currentPage.toString(),
                limit: itemsPerPage.toString()
            });
            
            if (statusFilter !== 'all') {
                params.append('status', statusFilter);
            }

            const response = await apiGet(`/api/download/sessions/${session.id}/items?${params}`);
            if (response.success) {
                setDownloadItems(response.data.items);
            }
        } catch (error) {
            console.error('Error loading download items:', error);
        }
    };

    /**
     * Format file size
     */
    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    /**
     * Format duration
     */
    const formatDuration = (seconds) => {
        if (!seconds) return '-';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    };

    /**
     * Get status icon
     */
    const getStatusIcon = (status) => {
        switch (status) {
            case 'pending': return '⏳';
            case 'downloading': return '📥';
            case 'completed': return '✅';
            case 'failed': return '❌';
            case 'skipped': return '⏭️';
            default: return '❓';
        }
    };

    /**
     * Get status class
     */
    const getStatusClass = (status) => {
        switch (status) {
            case 'pending': return 'status-pending';
            case 'downloading': return 'status-downloading';
            case 'completed': return 'status-completed';
            case 'failed': return 'status-failed';
            case 'skipped': return 'status-skipped';
            default: return 'status-unknown';
        }
    };

    if (loading) {
        return (
            <div className="download-progress loading">
                <div className="loading-spinner"></div>
                <p>Loading progress...</p>
            </div>
        );
    }

    if (!progressData) {
        return (
            <div className="download-progress error">
                <p>Failed to load progress data</p>
            </div>
        );
    }

    const { session: sessionData, progress } = progressData;
    const progressPercentage = sessionData.progress_percentage || 0;
    const sizeProgressPercentage = sessionData.size_progress_percentage || 0;

    return (
        <div className="download-progress">
            <div className="progress-header">
                <h2>Download Progress</h2>
                <div className="session-status">
                    <span className={`status-badge ${getStatusClass(sessionData.status)}`}>
                        {sessionData.status}
                    </span>
                </div>
            </div>

            {/* Overall Progress */}
            <div className="overall-progress">
                <div className="progress-stats">
                    <div className="stat-card">
                        <div className="stat-value">{sessionData.downloaded_files || 0}</div>
                        <div className="stat-label">Downloaded</div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-value">{sessionData.total_files || 0}</div>
                        <div className="stat-label">Total Files</div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-value">{formatFileSize(sessionData.downloaded_size || 0)}</div>
                        <div className="stat-label">Downloaded Size</div>
                    </div>
                    <div className="stat-card">
                        <div className="stat-value">{formatFileSize(sessionData.total_size || 0)}</div>
                        <div className="stat-label">Total Size</div>
                    </div>
                </div>

                <div className="progress-bars">
                    <div className="progress-bar-container">
                        <div className="progress-label">
                            Files Progress: {progressPercentage.toFixed(1)}%
                        </div>
                        <div className="progress-bar">
                            <div 
                                className="progress-fill"
                                style={{ width: `${progressPercentage}%` }}
                            ></div>
                        </div>
                    </div>

                    <div className="progress-bar-container">
                        <div className="progress-label">
                            Size Progress: {sizeProgressPercentage.toFixed(1)}%
                        </div>
                        <div className="progress-bar">
                            <div 
                                className="progress-fill size-progress"
                                style={{ width: `${sizeProgressPercentage}%` }}
                            ></div>
                        </div>
                    </div>
                </div>

                {/* Current Activity */}
                {sessionData.current_file_name && (
                    <div className="current-activity">
                        <div className="activity-icon">📥</div>
                        <div className="activity-text">
                            <div className="current-user">{sessionData.current_user_email}</div>
                            <div className="current-file">{sessionData.current_file_name}</div>
                        </div>
                    </div>
                )}

                {/* Duration */}
                <div className="session-duration">
                    <span>Duration: {formatDuration(sessionData.duration_seconds)}</span>
                </div>
            </div>

            {/* Status Summary */}
            <div className="status-summary">
                <div className="status-item">
                    <span className="status-icon">⏳</span>
                    <span className="status-count">{progress.pending || 0}</span>
                    <span className="status-text">Pending</span>
                </div>
                <div className="status-item">
                    <span className="status-icon">📥</span>
                    <span className="status-count">{progress.downloading || 0}</span>
                    <span className="status-text">Downloading</span>
                </div>
                <div className="status-item">
                    <span className="status-icon">✅</span>
                    <span className="status-count">{progress.completed || 0}</span>
                    <span className="status-text">Completed</span>
                </div>
                <div className="status-item">
                    <span className="status-icon">❌</span>
                    <span className="status-count">{progress.failed || 0}</span>
                    <span className="status-text">Failed</span>
                </div>
                <div className="status-item">
                    <span className="status-icon">⏭️</span>
                    <span className="status-count">{progress.skipped || 0}</span>
                    <span className="status-text">Skipped</span>
                </div>
            </div>

            {/* Control Buttons */}
            {sessionData.status === 'running' && (
                <div className="control-buttons">
                    <button className="btn btn-warning" onClick={onPause}>
                        ⏸️ Pause Download
                    </button>
                    <button className="btn btn-danger" onClick={onCancel}>
                        ❌ Cancel Download
                    </button>
                </div>
            )}

            {/* Download Items */}
            <div className="download-items">
                <div className="items-header">
                    <h3>Download Items</h3>
                    <div className="items-filter">
                        <select 
                            value={statusFilter} 
                            onChange={(e) => setStatusFilter(e.target.value)}
                        >
                            <option value="all">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="downloading">Downloading</option>
                            <option value="completed">Completed</option>
                            <option value="failed">Failed</option>
                            <option value="skipped">Skipped</option>
                        </select>
                    </div>
                </div>

                <div className="items-list">
                    {downloadItems.map((item) => (
                        <div key={item.id} className="download-item">
                            <div className="item-status">
                                <span className={`status-icon ${getStatusClass(item.status)}`}>
                                    {getStatusIcon(item.status)}
                                </span>
                            </div>
                            <div className="item-info">
                                <div className="item-name">{item.file_name}</div>
                                <div className="item-path">{item.file_path}</div>
                                <div className="item-user">{item.user_email}</div>
                            </div>
                            <div className="item-details">
                                <div className="item-size">{formatFileSize(item.file_size)}</div>
                                {item.error_message && (
                                    <div className="item-error" title={item.error_message}>
                                        Error: {item.error_message.substring(0, 50)}...
                                    </div>
                                )}
                                {item.retry_count > 0 && (
                                    <div className="item-retry">
                                        Retries: {item.retry_count}
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>

                {downloadItems.length === 0 && (
                    <div className="no-items">
                        No items found for the selected filter
                    </div>
                )}
            </div>
        </div>
    );
};

export default DownloadProgress;
