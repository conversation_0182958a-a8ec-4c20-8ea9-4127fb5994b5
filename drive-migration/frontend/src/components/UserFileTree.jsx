import React, { useState, useMemo } from 'react';
import './UserFileTree.css';

function UserFileTree({ user, files, loading, onError }) {
    const [expandedFolders, setExpandedFolders] = useState(new Set(['root']));
    const [selectedItems, setSelectedItems] = useState(new Set());
    const [sortBy, setSortBy] = useState('name'); // name, size, modified
    const [sortOrder, setSortOrder] = useState('asc'); // asc, desc
    const [showHidden, setShowHidden] = useState(false);
    const [viewMode, setViewMode] = useState('tree'); // tree, list

    // Build tree structure from flat file list
    const fileTree = useMemo(() => {
        if (!files || files.length === 0) return null;

        const tree = { id: 'root', name: 'Drive Root', children: {}, files: [], type: 'folder' };
        
        files.forEach(file => {
            const pathParts = file.path ? file.path.split('/').filter(Boolean) : [];
            let currentNode = tree;
            
            // Navigate/create folder structure
            pathParts.forEach((part, index) => {
                if (!currentNode.children[part]) {
                    currentNode.children[part] = {
                        id: `${currentNode.id}/${part}`,
                        name: part,
                        children: {},
                        files: [],
                        type: 'folder',
                        path: pathParts.slice(0, index + 1).join('/')
                    };
                }
                currentNode = currentNode.children[part];
            });
            
            // Add file to final folder
            currentNode.files.push(file);
        });

        return tree;
    }, [files]);

    const sortFiles = (items) => {
        if (!items) return [];
        
        return [...items].sort((a, b) => {
            let comparison = 0;
            
            // Folders first
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;
            
            switch (sortBy) {
                case 'name':
                    comparison = a.name.localeCompare(b.name, 'vi', { numeric: true });
                    break;
                case 'size':
                    comparison = (a.size || 0) - (b.size || 0);
                    break;
                case 'modified':
                    comparison = new Date(a.modifiedTime || 0) - new Date(b.modifiedTime || 0);
                    break;
                default:
                    comparison = 0;
            }
            
            return sortOrder === 'asc' ? comparison : -comparison;
        });
    };

    const toggleFolder = (folderId) => {
        const newExpanded = new Set(expandedFolders);
        if (newExpanded.has(folderId)) {
            newExpanded.delete(folderId);
        } else {
            newExpanded.add(folderId);
        }
        setExpandedFolders(newExpanded);
    };

    const toggleSelection = (itemId) => {
        const newSelected = new Set(selectedItems);
        if (newSelected.has(itemId)) {
            newSelected.delete(itemId);
        } else {
            newSelected.add(itemId);
        }
        setSelectedItems(newSelected);
    };

    const selectAll = () => {
        const allIds = new Set();
        const collectIds = (node) => {
            allIds.add(node.id);
            Object.values(node.children || {}).forEach(collectIds);
            (node.files || []).forEach(file => allIds.add(file.id));
        };
        if (fileTree) collectIds(fileTree);
        setSelectedItems(allIds);
    };

    const deselectAll = () => {
        setSelectedItems(new Set());
    };

    const formatFileSize = (bytes) => {
        if (!bytes) return '-';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleDateString('vi-VN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getFileIcon = (file) => {
        if (file.mimeType === 'application/vnd.google-apps.folder') return '📁';
        if (file.mimeType?.includes('image')) return '🖼️';
        if (file.mimeType?.includes('video')) return '🎥';
        if (file.mimeType?.includes('audio')) return '🎵';
        if (file.mimeType?.includes('pdf')) return '📄';
        if (file.mimeType?.includes('document')) return '📝';
        if (file.mimeType?.includes('spreadsheet')) return '📊';
        if (file.mimeType?.includes('presentation')) return '📽️';
        if (file.mimeType?.includes('text')) return '📃';
        return '📄';
    };

    const renderTreeNode = (node, level = 0) => {
        if (!node) return null;
        
        const isExpanded = expandedFolders.has(node.id);
        const isSelected = selectedItems.has(node.id);
        const childFolders = Object.values(node.children || {});
        const nodeFiles = node.files || [];
        const allItems = sortFiles([...childFolders, ...nodeFiles]);

        return (
            <div key={node.id} className="tree-node">
                {node.id !== 'root' && (
                    <div 
                        className={`tree-item folder ${isSelected ? 'selected' : ''}`}
                        style={{ paddingLeft: `${level * 1.5}rem` }}
                        onClick={() => toggleSelection(node.id)}
                    >
                        <div className="tree-item-content">
                            <button
                                className="folder-toggle"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    toggleFolder(node.id);
                                }}
                            >
                                {isExpanded ? '📂' : '📁'}
                            </button>
                            <span className="item-name">{node.name}</span>
                            <span className="item-count">
                                ({childFolders.length + nodeFiles.length} mục)
                            </span>
                        </div>
                    </div>
                )}

                {(node.id === 'root' || isExpanded) && (
                    <div className="tree-children">
                        {allItems.map(item => 
                            item.type === 'folder' 
                                ? renderTreeNode(item, level + 1)
                                : renderFileItem(item, level + 1)
                        )}
                    </div>
                )}
            </div>
        );
    };

    const renderFileItem = (file, level = 0) => {
        const isSelected = selectedItems.has(file.id);
        
        return (
            <div
                key={file.id}
                className={`tree-item file ${isSelected ? 'selected' : ''}`}
                style={{ paddingLeft: `${level * 1.5}rem` }}
                onClick={() => toggleSelection(file.id)}
            >
                <div className="tree-item-content">
                    <span className="file-icon">{getFileIcon(file)}</span>
                    <span className="item-name" title={file.name}>{file.name}</span>
                    <div className="file-info">
                        <span className="file-size">{formatFileSize(file.size)}</span>
                        <span className="file-date">{formatDate(file.modifiedTime)}</span>
                    </div>
                </div>
            </div>
        );
    };

    if (loading) {
        return (
            <div className="file-tree-loading">
                <div className="loading-spinner"></div>
                <p>Đang tải cấu trúc thư mục...</p>
            </div>
        );
    }

    if (!fileTree || files.length === 0) {
        return (
            <div className="file-tree-empty">
                <div className="empty-icon">📂</div>
                <h3>Không có dữ liệu</h3>
                <p>Người dùng này chưa có file hoặc thư mục nào, hoặc chưa được quét.</p>
            </div>
        );
    }

    return (
        <div className="user-file-tree">
            <div className="file-tree-controls">
                <div className="control-group">
                    <label>Sắp xếp:</label>
                    <select 
                        value={sortBy} 
                        onChange={(e) => setSortBy(e.target.value)}
                        className="sort-select"
                    >
                        <option value="name">Tên</option>
                        <option value="size">Kích thước</option>
                        <option value="modified">Ngày sửa</option>
                    </select>
                    <button
                        className={`sort-order ${sortOrder}`}
                        onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                        title={sortOrder === 'asc' ? 'Tăng dần' : 'Giảm dần'}
                    >
                        {sortOrder === 'asc' ? '↑' : '↓'}
                    </button>
                </div>

                <div className="control-group">
                    <label>Hiển thị:</label>
                    <button
                        className={`view-toggle ${viewMode === 'tree' ? 'active' : ''}`}
                        onClick={() => setViewMode('tree')}
                    >
                        🌳 Cây
                    </button>
                    <button
                        className={`view-toggle ${viewMode === 'list' ? 'active' : ''}`}
                        onClick={() => setViewMode('list')}
                    >
                        📋 Danh sách
                    </button>
                </div>

                <div className="control-group">
                    <button className="btn-action" onClick={selectAll}>
                        Chọn tất cả
                    </button>
                    <button className="btn-action" onClick={deselectAll}>
                        Bỏ chọn
                    </button>
                    <span className="selection-count">
                        {selectedItems.size} mục được chọn
                    </span>
                </div>
            </div>

            <div className={`file-tree-content ${viewMode}`}>
                {viewMode === 'tree' ? (
                    renderTreeNode(fileTree)
                ) : (
                    <div className="file-list-view">
                        {sortFiles(files).map(file => renderFileItem(file, 0))}
                    </div>
                )}
            </div>
        </div>
    );
}

export default UserFileTree;
