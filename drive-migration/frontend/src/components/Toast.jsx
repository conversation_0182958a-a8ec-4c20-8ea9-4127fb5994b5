import React, { useState, useEffect } from 'react';

const Toast = ({ 
  message, 
  type = 'error', // 'error', 'success', 'warning', 'info'
  duration = 5000,
  onClose,
  showDetails = false,
  details = null
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      if (onClose) onClose();
    }, 300); // Wait for animation
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      case 'error':
      default:
        return '❌';
    }
  };

  const getTypeClass = () => {
    switch (type) {
      case 'success':
        return 'toast-success';
      case 'warning':
        return 'toast-warning';
      case 'info':
        return 'toast-info';
      case 'error':
      default:
        return 'toast-error';
    }
  };

  if (!isVisible) return null;

  return (
    <div className={`toast ${getTypeClass()} ${isVisible ? 'toast-visible' : 'toast-hidden'}`}>
      <div className="toast-content">
        <div className="toast-header">
          <span className="toast-icon">{getIcon()}</span>
          <span className="toast-message">{message}</span>
          <div className="toast-actions">
            {showDetails && details && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="toast-details-btn"
                aria-label={isExpanded ? 'Ẩn chi tiết' : 'Hiện chi tiết'}
              >
                {isExpanded ? '▼' : '▶'}
              </button>
            )}
            <button
              onClick={handleClose}
              className="toast-close-btn"
              aria-label="Đóng"
            >
              ✕
            </button>
          </div>
        </div>
        
        {isExpanded && details && (
          <div className="toast-details">
            <pre className="toast-details-content">{details}</pre>
          </div>
        )}
      </div>
    </div>
  );
};

// Toast Container Component
export const ToastContainer = ({ toasts, onRemoveToast }) => {
  return (
    <div className="toast-container">
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          duration={toast.duration}
          showDetails={toast.showDetails}
          details={toast.details}
          onClose={() => onRemoveToast(toast.id)}
        />
      ))}
    </div>
  );
};

// Hook for managing toasts
export const useToast = () => {
  const [toasts, setToasts] = useState([]);

  const addToast = (message, type = 'error', options = {}) => {
    const id = Date.now() + Math.random();
    const toast = {
      id,
      message,
      type,
      duration: options.duration || 5000,
      showDetails: options.showDetails || false,
      details: options.details || null,
      ...options
    };

    setToasts(prev => [...prev, toast]);
    return id;
  };

  const removeToast = (id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const clearToasts = () => {
    setToasts([]);
  };

  // Helper methods for different toast types
  const showError = (message, options = {}) => {
    return addToast(message, 'error', options);
  };

  const showSuccess = (message, options = {}) => {
    return addToast(message, 'success', options);
  };

  const showWarning = (message, options = {}) => {
    return addToast(message, 'warning', options);
  };

  const showInfo = (message, options = {}) => {
    return addToast(message, 'info', options);
  };

  return {
    toasts,
    addToast,
    removeToast,
    clearToasts,
    showError,
    showSuccess,
    showWarning,
    showInfo
  };
};

export default Toast;
