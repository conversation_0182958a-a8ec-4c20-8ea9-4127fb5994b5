/* User File Tree Component Styles */
.user-file-tree {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.file-tree-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #718096;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.file-tree-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    color: #718096;
}

.file-tree-empty .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.file-tree-empty h3 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
}

.file-tree-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
    flex-wrap: wrap;
    gap: 1rem;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-size: 0.85rem;
    color: #4a5568;
    font-weight: 500;
    white-space: nowrap;
}

.sort-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 0.85rem;
    background: white;
}

.sort-order {
    width: 24px;
    height: 24px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.sort-order:hover {
    background: #f7fafc;
    border-color: #4299e1;
}

.view-toggle {
    padding: 0.25rem 0.5rem;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.view-toggle:hover {
    background: #f7fafc;
}

.view-toggle.active {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
}

.btn-action {
    padding: 0.25rem 0.75rem;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.btn-action:hover {
    background: #f7fafc;
    border-color: #4299e1;
}

.selection-count {
    font-size: 0.8rem;
    color: #718096;
    font-weight: 500;
}

.file-tree-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

.tree-node {
    margin-bottom: 0.25rem;
}

.tree-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    margin: 0 0.75rem;
}

.tree-item:hover {
    background: #f7fafc;
}

.tree-item.selected {
    background: #ebf8ff;
    border: 1px solid #4299e1;
}

.tree-item-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    min-width: 0;
}

.folder-toggle {
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    transition: background 0.2s ease;
}

.folder-toggle:hover {
    background: #e2e8f0;
}

.file-icon {
    font-size: 1rem;
    flex-shrink: 0;
}

.item-name {
    flex: 1;
    font-size: 0.9rem;
    color: #2d3748;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.item-count {
    font-size: 0.75rem;
    color: #718096;
    margin-left: 0.5rem;
}

.file-info {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-left: auto;
    font-size: 0.75rem;
    color: #718096;
}

.file-size,
.file-date {
    white-space: nowrap;
}

.tree-children {
    border-left: 1px solid #e2e8f0;
    margin-left: 1.5rem;
}

/* List view specific styles */
.file-list-view {
    display: flex;
    flex-direction: column;
}

.file-tree-content.list .tree-item {
    margin: 0 0.75rem 0.25rem 0.75rem;
    padding-left: 1rem !important;
}

.file-tree-content.list .file-info {
    gap: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .file-tree-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        padding: 1rem;
    }
    
    .control-group {
        justify-content: space-between;
        flex-wrap: wrap;
    }
    
    .tree-item {
        padding: 0.75rem 0.5rem;
        margin: 0 0.5rem;
    }
    
    .file-info {
        flex-direction: column;
        gap: 0.25rem;
        text-align: right;
    }
    
    .tree-children {
        margin-left: 1rem;
    }
}

@media (max-width: 640px) {
    .tree-item-content {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .file-info {
        flex-direction: row;
        justify-content: space-between;
        margin-left: 0;
    }
    
    .item-count {
        margin-left: 0;
    }
}

/* Scrollbar styling */
.file-tree-content::-webkit-scrollbar {
    width: 8px;
}

.file-tree-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.file-tree-content::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

.file-tree-content::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Animation for tree expansion */
.tree-children {
    animation: treeExpand 0.2s ease;
}

@keyframes treeExpand {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Selection animation */
.tree-item.selected {
    animation: itemSelect 0.3s ease;
}

@keyframes itemSelect {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* File type specific icons and colors */
.tree-item.folder .item-name {
    font-weight: 500;
}

.tree-item.file .item-name {
    color: #4a5568;
}

/* Context menu preparation */
.tree-item {
    position: relative;
}

.tree-item:focus {
    outline: none;
    background: #ebf8ff;
    border: 1px solid #4299e1;
}
