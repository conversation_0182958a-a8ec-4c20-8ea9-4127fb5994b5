/* User List Component Styles */
.user-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.user-list.grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.user-list.list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.user-list-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: #718096;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.user-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: #718096;
}

.user-list-empty .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    gap: 1rem;
}

.user-item:hover {
    border-color: #4299e1;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.15);
    transform: translateY(-1px);
}

.user-item.selected {
    border-color: #4299e1;
    background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
}

.user-item:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.user-avatar {
    position: relative;
    flex-shrink: 0;
}

.user-avatar img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e2e8f0;
}

.user-initials {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4299e1, #667eea);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
    border: 2px solid #e2e8f0;
}

.selection-indicator {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 20px;
    height: 20px;
    background: #48bb78;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-info {
    flex: 1;
    min-width: 0; /* Allows text truncation */
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.user-primary {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.95rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-email {
    color: #718096;
    font-size: 0.85rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: #718096;
}

.stat-icon {
    font-size: 0.7rem;
}

.stat-value {
    font-weight: 500;
}

.user-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
    align-self: flex-start;
}

.status-active {
    background: #c6f6d5;
    color: #22543d;
}

.status-inactive {
    background: #fef5e7;
    color: #744210;
}

.status-blocked {
    background: #fed7d7;
    color: #742a2a;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-action {
    width: 32px;
    height: 32px;
    border: none;
    background: #f7fafc;
    color: #4a5568;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-action:hover {
    background: #e2e8f0;
    color: #2d3748;
    transform: scale(1.05);
}

.btn-action:active {
    transform: scale(0.95);
}

/* List view specific styles */
.user-list.list .user-item {
    padding: 0.75rem 1rem;
}

.user-list.list .user-avatar img,
.user-list.list .user-initials {
    width: 40px;
    height: 40px;
}

.user-list.list .user-info {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.user-list.list .user-primary {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
}

.user-list.list .user-stats {
    gap: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .user-item {
        padding: 0.75rem;
        gap: 0.75rem;
    }
    
    .user-stats {
        gap: 0.75rem;
    }
    
    .stat-item {
        font-size: 0.75rem;
    }
    
    .user-list.list .user-info {
        flex-direction: column;
        align-items: stretch;
    }
    
    .user-list.list .user-primary {
        flex-direction: column;
        align-items: stretch;
        gap: 0.25rem;
    }
}

/* Scrollbar styling */
.user-list::-webkit-scrollbar {
    width: 6px;
}

.user-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.user-list::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.user-list::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}
