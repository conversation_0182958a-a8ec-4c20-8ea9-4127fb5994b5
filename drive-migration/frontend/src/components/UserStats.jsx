import React from 'react';
import './UserStats.css';

function UserStats({ users, selectedUser, userFiles }) {
    const calculateStats = () => {
        const totalUsers = users.length;
        const activeUsers = users.filter(user => user.status === 'active').length;
        const totalFiles = users.reduce((sum, user) => sum + (user.total_files || 0), 0);
        const totalSize = users.reduce((sum, user) => sum + (user.total_size || 0), 0);
        
        return {
            totalUsers,
            activeUsers,
            totalFiles,
            totalSize,
            selectedUserFiles: userFiles.length,
            selectedUserSize: userFiles.reduce((sum, file) => sum + (file.size || 0), 0)
        };
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    const stats = calculateStats();

    return (
        <div className="user-stats">
            <div className="stats-section global-stats">
                <h3>📊 Thống kê tổng quan</h3>
                <div className="stats-grid">
                    <div className="stat-card">
                        <div className="stat-icon">👥</div>
                        <div className="stat-content">
                            <div className="stat-value">{stats.totalUsers}</div>
                            <div className="stat-label">Tổng người dùng</div>
                        </div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="stat-icon">🟢</div>
                        <div className="stat-content">
                            <div className="stat-value">{stats.activeUsers}</div>
                            <div className="stat-label">Đang hoạt động</div>
                        </div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="stat-icon">📁</div>
                        <div className="stat-content">
                            <div className="stat-value">{stats.totalFiles.toLocaleString()}</div>
                            <div className="stat-label">Tổng files</div>
                        </div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="stat-icon">💾</div>
                        <div className="stat-content">
                            <div className="stat-value">{formatFileSize(stats.totalSize)}</div>
                            <div className="stat-label">Tổng dung lượng</div>
                        </div>
                    </div>
                </div>
            </div>

            {selectedUser && (
                <div className="stats-section selected-stats">
                    <h3>🎯 Thông tin được chọn</h3>
                    <div className="selected-user-info">
                        <div className="selected-user-header">
                            <div className="selected-user-avatar">
                                {selectedUser.avatar ? (
                                    <img src={selectedUser.avatar} alt={selectedUser.name || selectedUser.email} />
                                ) : (
                                    <div className="selected-user-initials">
                                        {(selectedUser.name || selectedUser.email).slice(0, 2).toUpperCase()}
                                    </div>
                                )}
                            </div>
                            <div className="selected-user-details">
                                <div className="selected-user-name">
                                    {selectedUser.name || selectedUser.email.split('@')[0]}
                                </div>
                                <div className="selected-user-email">{selectedUser.email}</div>
                            </div>
                        </div>
                        
                        <div className="selected-stats-grid">
                            <div className="selected-stat">
                                <span className="selected-stat-icon">📁</span>
                                <span className="selected-stat-value">{stats.selectedUserFiles}</span>
                                <span className="selected-stat-label">files đã tải</span>
                            </div>
                            <div className="selected-stat">
                                <span className="selected-stat-icon">💾</span>
                                <span className="selected-stat-value">{formatFileSize(stats.selectedUserSize)}</span>
                                <span className="selected-stat-label">dung lượng</span>
                            </div>
                            <div className="selected-stat">
                                <span className="selected-stat-icon">📂</span>
                                <span className="selected-stat-value">
                                    {userFiles.filter(file => file.mimeType === 'application/vnd.google-apps.folder').length}
                                </span>
                                <span className="selected-stat-label">thư mục</span>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default UserStats;
