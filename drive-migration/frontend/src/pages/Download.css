.download-page {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* Actions Bar */
.actions-bar {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: flex-start;
}

/* Sessions Overview */
.sessions-overview {
    width: 100%;
}

.sessions-list h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

/* Sessions Grid */
.sessions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
}

.session-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.session-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.session-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: #ffeaa7;
    color: #d63031;
}

.status-running {
    background: #74b9ff;
    color: white;
}

.status-paused {
    background: #fdcb6e;
    color: #e17055;
}

.status-completed {
    background: #00b894;
    color: white;
}

.status-failed {
    background: #d63031;
    color: white;
}

.status-cancelled {
    background: #636e72;
    color: white;
}

.status-unknown {
    background: #ddd;
    color: #666;
}

/* Session Stats */
.session-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.stat .label {
    font-weight: 600;
    color: #6c757d;
}

.stat .value {
    color: #2c3e50;
    font-weight: 500;
}

/* Session Actions */
.session-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Session Detail */
.session-detail {
    width: 100%;
}

.session-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.session-nav h2 {
    margin: 0;
    color: #2c3e50;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .download-page {
        padding: 1rem;
    }
    
    .sessions-grid {
        grid-template-columns: 1fr;
    }
    
    .session-stats {
        grid-template-columns: 1fr;
    }
    
    .actions-bar {
        flex-direction: column;
    }
    
    .session-nav {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .session-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .session-card {
        padding: 1rem;
    }
    
    .session-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}
