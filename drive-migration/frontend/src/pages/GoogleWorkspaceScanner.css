/* User Management Page - kế thừa từ Migration.css */
@import './Migration.css';

/* User Management specific styles */
.users-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.users-table .table-header {
  display: grid;
  grid-template-columns: 80px 250px 250px 140px 150px 150px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
}

.users-table .table-header > div {
  padding: 1rem 0.75rem;
  border-right: 1px solid #e5e7eb;
}

.users-table .table-header > div:last-child {
  border-right: none;
}

.users-table .table-body {
  max-height: 600px;
  overflow-y: auto;
}

.users-table .table-row {
  display: grid;
  grid-template-columns: 80px 250px 250px 140px 150px 150px;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
}

.users-table .table-row:hover {
  background: #f9fafb;
}

.users-table .table-row.selected {
  background: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.users-table .table-row > div {
  padding: 0.75rem;
  border-right: 1px solid #f3f4f6;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.users-table .table-row > div:last-child {
  border-right: none;
}

/* User avatar styles */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

/* User status styles */
.user-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.user-status.active {
  background: #dcfce7;
  color: #166534;
}

.user-status.suspended {
  background: #fee2e2;
  color: #991b1b;
}

/* Scan progress specific styles */
.scan-progress-mini {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.scan-progress-mini h4 {
  margin: 0 0 0.5rem 0;
  color: #0f172a;
  font-size: 1rem;
}

.scan-progress-mini .progress-bar {
  height: 8px;
  background: #e0e7ff;
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.scan-progress-mini .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
}

.scan-progress-mini .progress-text {
  font-size: 0.875rem;
  color: #374151;
}

/* Control section improvements */
.controls-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.controls-section h2 {
  margin-bottom: 1.5rem;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.controls-section h2::before {
  content: "⚙️";
  font-size: 1.5rem;
}

.force-scan-option {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.force-scan-option .checkbox-label {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.force-scan-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
  margin-left: 2rem;
}

/* Stats cards */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stats-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-card .stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stats-card .stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stats-card.primary .stat-number {
  color: #3b82f6;
}

.stats-card.success .stat-number {
  color: #10b981;
}

.stats-card.warning .stat-number {
  color: #f59e0b;
}

/* Action buttons styling - GoogleWorkspaceScanner specific */
.controls-section .action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
  margin-top: 1.5rem;
}

.controls-section .action-buttons .btn {
  flex-shrink: 0;
}

/* Responsive adjustments for action buttons */
@media (max-width: 768px) {
  .controls-section .action-buttons {
    gap: 0.75rem;
    flex-direction: column;
    width: 100%;
  }
  
  .controls-section .action-buttons .btn {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .controls-section .action-buttons {
    gap: 0.5rem;
  }
}

/* Responsive design for user management */
@media (max-width: 1024px) {
  .users-table .table-header,
  .users-table .table-row {
    grid-template-columns: 80px 220px 200px 120px 140px 140px;
  }
}

@media (max-width: 768px) {
  .users-table .table-header,
  .users-table .table-row {
    grid-template-columns: 60px 1fr 150px 100px;
  }

  /* Hide some columns on mobile */
  .users-table .table-header > div:nth-child(5),
  .users-table .table-header > div:nth-child(6),
  .users-table .table-row > div:nth-child(5),
  .users-table .table-row > div:nth-child(6) {
    display: none;
  }

  .controls-section {
    padding: 1.5rem;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }

  .users-table .table-header,
  .users-table .table-row {
    grid-template-columns: 60px 1fr 100px;
  }

  /* Hide email column on very small screens */
  .users-table .table-header > div:nth-child(3),
  .users-table .table-row > div:nth-child(3) {
    display: none;
  }
}

/* Animation for loading states */
.users-table .table-row.loading {
  opacity: 0.6;
  pointer-events: none;
}

.users-table .table-row.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced error display for user management context */
.google-workspace-scanner-error {
  margin-bottom: 2rem;
}

.google-workspace-scanner-error .error-header {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.google-workspace-scanner-error .error-title::before {
  content: "👥 ";
  margin-right: 0.5rem;
}

/* Success states */
.scan-success {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border: 1px solid #10b981;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  color: #065f46;
}

.scan-success h4 {
  margin: 0 0 0.5rem 0;
  color: #065f46;
}

.scan-success .success-icon {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

/* Debug checkbox visibility */
.checkbox-custom {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.checkbox-custom::after {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure proper layout for checkbox containers */
.file-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  min-height: 18px;
}

.select-all-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.force-scan-option .checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

/* Checkbox styling fixes for GoogleWorkspaceScanner */
.file-checkbox .checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.file-checkbox input[type="checkbox"]:checked + .checkbox-custom {
  border-color: #3b82f6;
  background: #3b82f6;
}

.file-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Select all checkbox styling */
.select-all-checkbox .checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.select-all-checkbox input[type="checkbox"]:checked + .checkbox-custom {
  border-color: #3b82f6;
  background: #3b82f6;
}

.select-all-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Force scan checkbox styling */
.force-scan-option .checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.force-scan-option input[type="checkbox"]:checked + .checkbox-custom {
  border-color: #3b82f6;
  background: #3b82f6;
}

.force-scan-option input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Hover effects for checkboxes */
.file-checkbox:hover .checkbox-custom,
.select-all-checkbox:hover .checkbox-custom,
.force-scan-option .checkbox-label:hover .checkbox-custom {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.file-checkbox input[type="checkbox"]:disabled + .checkbox-custom,
.select-all-checkbox input[type="checkbox"]:disabled + .checkbox-custom,
.force-scan-option input[type="checkbox"]:disabled + .checkbox-custom {
  opacity: 0.5;
  cursor: not-allowed;
}

.file-checkbox input[type="checkbox"]:disabled + .checkbox-custom:hover,
.select-all-checkbox input[type="checkbox"]:disabled + .checkbox-custom:hover,
.force-scan-option input[type="checkbox"]:disabled + .checkbox-custom:hover {
  border-color: #d1d5db;
  box-shadow: none;
}

/* Ensure checkbox containers have proper cursor */
.file-checkbox,
.select-all-checkbox,
.force-scan-option .checkbox-label {
  cursor: pointer;
}

.file-checkbox input[type="checkbox"]:disabled,
.select-all-checkbox input[type="checkbox"]:disabled,
.force-scan-option input[type="checkbox"]:disabled {
  cursor: not-allowed;
}

.file-checkbox:has(input[type="checkbox"]:disabled),
.select-all-checkbox:has(input[type="checkbox"]:disabled),
.force-scan-option .checkbox-label:has(input[type="checkbox"]:disabled) {
  cursor: not-allowed;
}

/* High specificity checkbox fixes - IMPORTANT */
.users-table .file-checkbox input[type="checkbox"]:checked + .checkbox-custom::after,
.selection-summary .select-all-checkbox input[type="checkbox"]:checked + .checkbox-custom::after,
.controls-section .force-scan-option input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 12px !important;
  font-weight: bold !important;
  line-height: 1 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
  z-index: 1 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.users-table .file-checkbox .checkbox-custom,
.selection-summary .select-all-checkbox .checkbox-custom,
.controls-section .force-scan-option .checkbox-custom {
  background: transparent !important;
  border: 2px solid #d1d5db !important;
  position: relative !important;
}

.users-table .file-checkbox input[type="checkbox"]:checked + .checkbox-custom,
.selection-summary .select-all-checkbox input[type="checkbox"]:checked + .checkbox-custom,
.controls-section .force-scan-option input[type="checkbox"]:checked + .checkbox-custom {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

/* Alternative check mark using Unicode */
.users-table .file-checkbox input[type="checkbox"]:checked + .checkbox-custom::before {
  content: '✔' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 10px !important;
  font-weight: 900 !important;
  z-index: 2 !important;
  display: block !important;
}

/* Responsive design for user management */
@media (max-width: 1024px) {
  .users-table .table-header,
  .users-table .table-row {
    grid-template-columns: 80px 220px 200px 120px 140px 140px;
  }
}

@media (max-width: 768px) {
  .users-table .table-header,
  .users-table .table-row {
    grid-template-columns: 60px 1fr 150px 100px;
  }

  /* Hide some columns on mobile */
  .users-table .table-header > div:nth-child(5),
  .users-table .table-header > div:nth-child(6),
  .users-table .table-row > div:nth-child(5),
  .users-table .table-row > div:nth-child(6) {
    display: none;
  }

  .controls-section {
    padding: 1.5rem;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .stats-overview {
    grid-template-columns: 1fr;
  }

  .users-table .table-header,
  .users-table .table-row {
    grid-template-columns: 60px 1fr 100px;
  }

  /* Hide email column on very small screens */
  .users-table .table-header > div:nth-child(3),
  .users-table .table-row > div:nth-child(3) {
    display: none;
  }
}
