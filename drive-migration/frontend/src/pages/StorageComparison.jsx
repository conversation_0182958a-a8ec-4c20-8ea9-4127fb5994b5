import React, { useState, useEffect } from 'react';
import { apiGet, apiPost, apiPut, formatError } from '../utils/apiUtils';
import { useToast } from '../components/Toast';
import './StorageComparison.css';

function StorageComparison() {
    const [users, setUsers] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [overallStats, setOverallStats] = useState(null);

    // Filters and pagination
    const [search, setSearch] = useState('');
    const [sortBy, setSortBy] = useState('total_usage_bytes');
    const [sortOrder, setSortOrder] = useState('desc');
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(50);
    const [pagination, setPagination] = useState(null);

    // Local folder settings
    const [defaultFolderPath, setDefaultFolderPath] = useState('E:\\');
    const [calculatingLocal, setCalculatingLocal] = useState(new Set());

    // Scanning
    const [scanning, setScanning] = useState(false);
    const [calculatingAllLocal, setCalculatingAllLocal] = useState(false);

    const { showError, showSuccess, showWarning } = useToast();

    useEffect(() => {
        loadStorageData();
    }, [search, sortBy, sortOrder, page, pageSize]);

    const loadStorageData = async () => {
        setLoading(true);
        setError(null);

        try {
            const params = new URLSearchParams({
                search,
                sortBy,
                sortOrder,
                page: page.toString(),
                pageSize: pageSize.toString()
            });

            const response = await apiGet(`/api/storage/stats?${params}`);
            setUsers(response.users);
            setPagination(response.pagination);
            setOverallStats(response.overallStats);

        } catch (error) {
            console.error('Error loading storage data:', error);
            const errorInfo = formatError(error);
            setError(error);
            showError(`Lỗi tải dữ liệu: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details
            });
        } finally {
            setLoading(false);
        }
    };

    const handleScanStorage = async () => {
        setScanning(true);
        try {
            // First, get all users from scanned_users table
            let userEmails = [];

            if (users.length > 0) {
                // If we already have storage data, use those emails
                userEmails = users.map(user => user.user_email);
            } else {
                // If no storage data yet, get users from scanned_users
                try {
                    const usersResponse = await apiGet('/api/scan/users');
                    if (usersResponse.users && usersResponse.users.length > 0) {
                        userEmails = usersResponse.users.map(user => user.email);
                    } else {
                        showWarning('Không có user nào trong hệ thống. Vui lòng scan users trước.');
                        return;
                    }
                } catch (userError) {
                    showError('Không thể lấy danh sách users. Vui lòng scan users trước.');
                    return;
                }
            }

            showSuccess(`Bắt đầu scan storage cho ${userEmails.length} users...`);

            const response = await apiPost('/api/storage/scan', {
                userEmails,
                forceRefresh: true
            });

            showSuccess(`Scan hoàn thành: ${response.results.successful}/${response.results.totalUsers} thành công`);

            if (response.results.errors.length > 0) {
                showWarning(`${response.results.errors.length} users gặp lỗi khi scan`);
            }

            // Reload data
            await loadStorageData();

        } catch (error) {
            console.error('Error scanning storage:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi scan storage: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details
            });
        } finally {
            setScanning(false);
        }
    };

    const handleCalculateAllLocalSizes = async () => {
        setCalculatingAllLocal(true);

        try {
            showSuccess('Bắt đầu tính toán dung lượng local cho tất cả users...');

            const response = await apiPost('/api/storage/calculate-local-all', {
                folderPath: defaultFolderPath
            });

            showSuccess(`Tính toán hoàn thành: ${response.results.successful}/${response.results.totalUsers} thành công. Tổng: ${response.results.totalSizeFormatted}`);

            if (response.results.errors.length > 0) {
                showWarning(`${response.results.errors.length} users gặp lỗi khi tính toán`);
            }

            // Reload data to get updated local sizes
            await loadStorageData();

        } catch (error) {
            console.error('Error calculating all local sizes:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi tính toán dung lượng local: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details
            });
        } finally {
            setCalculatingAllLocal(false);
        }
    };

    const handleCalculateLocalSize = async (userEmail) => {
        setCalculatingLocal(prev => new Set(prev).add(userEmail));

        try {
            const response = await apiPost(`/api/storage/calculate-local/${userEmail}`, {
                folderPath: defaultFolderPath
            });

            showSuccess(`Đã tính toán dung lượng local cho ${userEmail}: ${response.folderSizeFormatted}`);

            // Update the user in the list
            setUsers(prevUsers =>
                prevUsers.map(user =>
                    user.user_email === userEmail
                        ? { ...user, local_downloaded_bytes: response.folderSize, local_folder_path: defaultFolderPath }
                        : user
                )
            );

        } catch (error) {
            console.error('Error calculating local size:', error);
            const errorInfo = formatError(error);
            showError(`Lỗi tính toán dung lượng local: ${errorInfo.message}`, {
                showDetails: true,
                details: errorInfo.details
            });
        } finally {
            setCalculatingLocal(prev => {
                const newSet = new Set(prev);
                newSet.delete(userEmail);
                return newSet;
            });
        }
    };

    const formatBytes = (bytes) => {
        if (!bytes || bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const calculateMissingBytes = (user) => {
        const driveUsage = user.drive_usage_bytes || 0;
        const localDownloaded = user.local_downloaded_bytes || 0;
        return Math.max(0, driveUsage - localDownloaded);
    };

    const getProgressPercentage = (user) => {
        const driveUsage = user.drive_usage_bytes || 0;
        const localDownloaded = user.local_downloaded_bytes || 0;

        if (driveUsage === 0) return 0;
        return Math.min(100, (localDownloaded / driveUsage) * 100);
    };

    const getProgressColor = (percentage) => {
        if (percentage >= 90) return '#4CAF50'; // Green
        if (percentage >= 70) return '#FF9800'; // Orange
        return '#F44336'; // Red
    };

    return (
        <div className="storage-comparison">
            <div className="page-header">
                <h1>📊 So sánh dung lượng Storage</h1>
                <p>Đối chiếu dung lượng đã download vs dung lượng trên Google Drive</p>
            </div>

            {/* Controls */}
            <div className="controls-section">
                <div className="controls-row">
                    <div className="search-box">
                        <input
                            type="text"
                            placeholder="Tìm kiếm theo email..."
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                            className="search-input"
                        />
                    </div>

                    <div className="sort-controls">
                        <select
                            value={sortBy}
                            onChange={(e) => setSortBy(e.target.value)}
                            className="sort-select"
                        >
                            <option value="total_usage_bytes">Tổng dung lượng</option>
                            <option value="drive_usage_bytes">Dung lượng Drive</option>
                            <option value="local_downloaded_bytes">Đã download</option>
                            <option value="user_email">Email</option>
                            <option value="last_scanned_at">Lần scan cuối</option>
                        </select>

                        <select
                            value={sortOrder}
                            onChange={(e) => setSortOrder(e.target.value)}
                            className="sort-select"
                        >
                            <option value="desc">Giảm dần</option>
                            <option value="asc">Tăng dần</option>
                        </select>
                    </div>

                    <button
                        onClick={handleScanStorage}
                        disabled={scanning || loading}
                        className="btn btn-primary"
                    >
                        {scanning ? '🔄 Đang scan...' : '🔍 Scan Storage'}
                    </button>

                    <button
                        onClick={handleCalculateAllLocalSizes}
                        disabled={calculatingAllLocal || loading}
                        className="btn btn-secondary"
                    >
                        {calculatingAllLocal ? '🔄 Đang tính toán...' : '📊 Tính toán tất cả Local'}
                    </button>
                </div>

                <div className="controls-row">
                    <div className="folder-path-setting">
                        <label>Đường dẫn thư mục local:</label>
                        <input
                            type="text"
                            value={defaultFolderPath}
                            onChange={(e) => setDefaultFolderPath(e.target.value)}
                            placeholder="E:\"
                            className="folder-path-input"
                        />
                    </div>
                </div>
            </div>

            {/* Overall Statistics */}
            {overallStats && (
                <div className="overall-stats">
                    <h3>📈 Thống kê tổng quan</h3>
                    <div className="stats-grid">
                        <div className="stat-item">
                            <span className="stat-label">Tổng số users:</span>
                            <span className="stat-value">{overallStats.totalUsers}</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-label">Tổng dung lượng Drive:</span>
                            <span className="stat-value">{formatBytes(overallStats.totalDriveUsed)}</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-label">Tổng dung lượng Gmail:</span>
                            <span className="stat-value">{formatBytes(overallStats.totalGmailUsed)}</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-label">Users có lỗi:</span>
                            <span className="stat-value error">{overallStats.usersWithErrors}</span>
                        </div>
                    </div>
                </div>
            )}

            {/* Users Table */}
            <div className="users-table-container">
                {loading && <div className="loading">🔄 Đang tải dữ liệu...</div>}

                {error && (
                    <div className="error-message">
                        ❌ Có lỗi xảy ra khi tải dữ liệu
                    </div>
                )}

                {!loading && !error && users.length === 0 && (
                    <div className="no-data">
                        📭 Không có dữ liệu storage. Vui lòng scan storage trước.
                    </div>
                )}

                {!loading && !error && users.length > 0 && (
                    <>
                        <table className="users-table">
                            <thead>
                                <tr>
                                    <th>Email</th>
                                    <th>Drive Usage</th>
                                    <th>Gmail Usage</th>
                                    <th>Total Usage</th>
                                    <th>Downloaded</th>
                                    <th>Missing</th>
                                    <th>Progress</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {users.map((user) => {
                                    const missingBytes = calculateMissingBytes(user);
                                    const progressPercentage = getProgressPercentage(user);
                                    const isCalculating = calculatingLocal.has(user.user_email);

                                    return (
                                        <tr key={user.user_email}>
                                            <td className="email-cell">
                                                <div className="email-info">
                                                    <span className="email">{user.user_email}</span>
                                                    {user.scan_error_message && (
                                                        <span className="error-indicator" title={user.scan_error_message}>
                                                            ⚠️
                                                        </span>
                                                    )}
                                                </div>
                                                {user.last_scanned_at && (
                                                    <div className="last-scanned">
                                                        Scan: {new Date(user.last_scanned_at).toLocaleString()}
                                                    </div>
                                                )}
                                            </td>
                                            <td>{formatBytes(user.drive_usage_bytes)}</td>
                                            <td>{formatBytes(user.gmail_usage_bytes)}</td>
                                            <td>{formatBytes(user.total_usage_bytes)}</td>
                                            <td className="downloaded-cell">
                                                <span>{formatBytes(user.local_downloaded_bytes)}</span>
                                                {user.local_folder_path && (
                                                    <div className="folder-path">
                                                        📁 {user.local_folder_path}{user.user_email}
                                                    </div>
                                                )}
                                            </td>
                                            <td className={missingBytes > 0 ? 'missing-bytes' : 'complete'}>
                                                {formatBytes(missingBytes)}
                                            </td>
                                            <td>
                                                <div className="progress-container">
                                                    <div
                                                        className="progress-bar"
                                                        style={{
                                                            width: `${progressPercentage}%`,
                                                            backgroundColor: getProgressColor(progressPercentage)
                                                        }}
                                                    ></div>
                                                    <span className="progress-text">
                                                        {progressPercentage.toFixed(1)}%
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <button
                                                    onClick={() => handleCalculateLocalSize(user.user_email)}
                                                    disabled={isCalculating}
                                                    className="btn btn-small"
                                                    title="Tính toán dung lượng đã download"
                                                >
                                                    {isCalculating ? '🔄' : '📊'}
                                                </button>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>

                        {/* Pagination */}
                        {pagination && pagination.totalPages > 1 && (
                            <div className="pagination">
                                <button
                                    onClick={() => setPage(page - 1)}
                                    disabled={page <= 1}
                                    className="btn btn-small"
                                >
                                    ← Trước
                                </button>

                                <span className="page-info">
                                    Trang {page} / {pagination.totalPages}
                                    ({pagination.totalCount} users)
                                </span>

                                <button
                                    onClick={() => setPage(page + 1)}
                                    disabled={page >= pagination.totalPages}
                                    className="btn btn-small"
                                >
                                    Sau →
                                </button>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
}

export default StorageComparison;
